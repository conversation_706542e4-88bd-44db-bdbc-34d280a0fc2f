{% extends "base.html" %}

{% block title %}Dashboard - Resume Optimizer{% endblock %}

{% block content %}
<div class="container">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>Welcome back, {{ user.first_name or user.email.split('@')[0] }}!</h2>
                    <p class="text-muted mb-0">Here's your resume optimization overview</p>
                </div>
                <div>
                    <a href="/resumes/create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create New Resume
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="display-4 text-primary mb-2">{{ dashboard.total_resumes }}</div>
                    <h6 class="card-title text-muted">Total Resumes</h6>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="display-4 text-success mb-2">{{ dashboard.optimized_resumes }}</div>
                    <h6 class="card-title text-muted">Optimized</h6>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    {% if dashboard.avg_optimization_score %}
                    <div class="display-4 text-info mb-2">{{ dashboard.avg_optimization_score }}%</div>
                    {% else %}
                    <div class="display-4 text-muted mb-2">--</div>
                    {% endif %}
                    <h6 class="card-title text-muted">Avg. Optimization</h6>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    {% if dashboard.avg_ats_score %}
                    <div class="display-4 text-warning mb-2">{{ dashboard.avg_ats_score }}%</div>
                    {% else %}
                    <div class="display-4 text-muted mb-2">--</div>
                    {% endif %}
                    <h6 class="card-title text-muted">Avg. ATS Score</h6>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Resumes -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Resumes</h5>
                    <a href="/resumes" class="btn btn-outline-primary btn-sm">View All</a>
                </div>
                <div class="card-body">
                    {% if dashboard.recent_resumes %}
                    <div class="list-group list-group-flush">
                        {% for resume in dashboard.recent_resumes %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ resume.title }}</h6>
                                <small class="text-muted">
                                    Updated {{ resume.updated_at.strftime('%b %d, %Y') if resume.updated_at else resume.created_at.strftime('%b %d, %Y') }}
                                </small>
                                {% if resume.original_filename %}
                                <br><small class="text-muted">{{ resume.original_filename }}</small>
                                {% endif %}
                            </div>
                            <div class="d-flex align-items-center">
                                {% if resume.optimization_score %}
                                <div class="me-3">
                                    <div class="progress" style="width: 60px; height: 8px;">
                                        <div class="progress-bar 
                                            {% if resume.optimization_score >= 80 %}bg-success
                                            {% elif resume.optimization_score >= 60 %}bg-warning
                                            {% else %}bg-danger{% endif %}" 
                                            style="width: {{ resume.optimization_score }}%"></div>
                                    </div>
                                    <small class="text-muted">{{ resume.optimization_score }}%</small>
                                </div>
                                {% endif %}
                                <div class="btn-group" role="group">
                                    <a href="/resumes/{{ resume.id }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/resumes/{{ resume.id }}/edit" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No resumes yet</h6>
                        <p class="text-muted mb-3">Get started by creating your first resume</p>
                        <a href="/resumes/create" class="btn btn-primary">Create Resume</a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/resumes/upload" class="btn btn-outline-primary">
                            <i class="fas fa-upload me-2"></i>Upload Resume
                        </a>
                        <a href="/resumes/create" class="btn btn-outline-success">
                            <i class="fas fa-plus me-2"></i>Create New Resume
                        </a>
                        <a href="/analyzer" class="btn btn-outline-info">
                            <i class="fas fa-search me-2"></i>Analyze Job Description
                        </a>
                        <a href="/templates" class="btn btn-outline-warning">
                            <i class="fas fa-file-alt me-2"></i>Browse Templates
                        </a>
                    </div>
                </div>
            </div>

            <!-- Tips & Insights -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Tips & Insights</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">
                            <i class="fas fa-lightbulb me-2"></i>Optimization Tip
                        </h6>
                        <p class="small mb-0">
                            {% if dashboard.avg_optimization_score and dashboard.avg_optimization_score < 70 %}
                            Consider using more industry-specific keywords and quantifying your achievements with numbers and percentages.
                            {% elif dashboard.avg_optimization_score and dashboard.avg_optimization_score < 85 %}
                            Great progress! Try tailoring your resume to specific job descriptions for even better results.
                            {% elif dashboard.avg_optimization_score %}
                            Excellent work! Your resumes are well-optimized. Keep updating them with new achievements.
                            {% else %}
                            Upload or create a resume to get personalized optimization tips powered by AI.
                            {% endif %}
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-success">
                            <i class="fas fa-robot me-2"></i>AI Insight
                        </h6>
                        <p class="small mb-0">
                            {% if dashboard.total_resumes == 0 %}
                            Start by uploading your current resume or creating a new one. Our AI will analyze it and provide optimization suggestions.
                            {% elif dashboard.optimized_resumes == 0 %}
                            Use our AI optimization feature to improve your resume's ATS compatibility and keyword density.
                            {% else %}
                            Regular optimization helps keep your resume competitive. Consider re-optimizing monthly or when applying to new roles.
                            {% endif %}
                        </p>
                    </div>
                    
                    <div>
                        <h6 class="text-info">
                            <i class="fas fa-chart-line me-2"></i>Progress Tracking
                        </h6>
                        <p class="small mb-0">
                            {% if dashboard.total_resumes >= 3 %}
                            You're building a strong resume portfolio! Consider creating role-specific versions for different job applications.
                            {% elif dashboard.total_resumes >= 1 %}
                            Good start! Consider creating additional resume versions tailored to different job types or industries.
                            {% else %}
                            Track your resume optimization progress and see improvements over time with our analytics dashboard.
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
