"""Resume router for resume management and file upload."""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Request, UploadFile, File, Form
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session

from app.database import get_db
from app.services.resume_service import ResumeService
from app.schemas.resume import (
    ResumeCreate, ResumeUpdate, ResumeResponse, ResumeListResponse, 
    FileUploadResponse, ResumeTemplateResponse
)
from app.schemas.auth import UserResponse
from app.routers.auth import get_current_user
from app.config import settings

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")


# Web routes (HTML responses)
@router.get("/", response_class=HTMLResponse)
async def resumes_page(
    request: Request, 
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Display user's resumes page."""
    resume_service = ResumeService(db)
    resumes = resume_service.get_user_resumes(current_user.id)
    
    return templates.TemplateResponse(
        "resumes/list.html",
        {"request": request, "user": current_user, "resumes": resumes}
    )


@router.get("/upload", response_class=HTMLResponse)
async def upload_page(
    request: Request,
    current_user: UserResponse = Depends(get_current_user)
):
    """Display resume upload page."""
    return templates.TemplateResponse(
        "resumes/upload.html",
        {"request": request, "user": current_user}
    )


@router.get("/create", response_class=HTMLResponse)
async def create_page(
    request: Request,
    current_user: UserResponse = Depends(get_current_user)
):
    """Display resume creation form."""
    return templates.TemplateResponse(
        "resumes/create.html",
        {"request": request, "user": current_user}
    )


@router.get("/{resume_id}", response_class=HTMLResponse)
async def resume_detail_page(
    resume_id: int,
    request: Request,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Display resume detail page."""
    resume_service = ResumeService(db)
    resume = resume_service.get_resume_by_id(resume_id, current_user.id)
    
    if not resume:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Resume not found"
        )
    
    return templates.TemplateResponse(
        "resumes/detail.html",
        {"request": request, "user": current_user, "resume": resume}
    )


@router.get("/{resume_id}/edit", response_class=HTMLResponse)
async def edit_resume_page(
    resume_id: int,
    request: Request,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Display resume edit page."""
    resume_service = ResumeService(db)
    resume = resume_service.get_resume_by_id(resume_id, current_user.id)
    
    if not resume:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Resume not found"
        )
    
    return templates.TemplateResponse(
        "resumes/edit.html",
        {"request": request, "user": current_user, "resume": resume}
    )


# API routes (JSON responses)
@router.get("/api/list", response_model=List[ResumeListResponse])
async def get_user_resumes(
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get list of user's resumes."""
    resume_service = ResumeService(db)
    resumes = resume_service.get_user_resumes(current_user.id)
    return [ResumeListResponse.model_validate(resume) for resume in resumes]


@router.get("/api/{resume_id}", response_model=ResumeResponse)
async def get_resume(
    resume_id: int,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get resume by ID."""
    resume_service = ResumeService(db)
    resume = resume_service.get_resume_by_id(resume_id, current_user.id)
    
    if not resume:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Resume not found"
        )
    
    return ResumeResponse.model_validate(resume)


@router.post("/api/upload", response_model=FileUploadResponse)
async def upload_resume(
    file: UploadFile = File(...),
    title: str = Form(None),
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload and parse resume file."""
    resume_service = ResumeService(db)
    
    try:
        resume = await resume_service.create_resume_from_upload(
            file=file,
            user_id=current_user.id,
            title=title
        )
        
        # Get parsed sections for response
        parsed_sections = []
        if resume.personal_info:
            parsed_sections.append("Personal Information")
        if resume.summary:
            parsed_sections.append("Summary")
        if resume.work_experience:
            parsed_sections.append("Work Experience")
        if resume.education:
            parsed_sections.append("Education")
        if resume.skills:
            parsed_sections.append("Skills")
        if resume.certifications:
            parsed_sections.append("Certifications")
        if resume.projects:
            parsed_sections.append("Projects")
        
        return FileUploadResponse(
            message="Resume uploaded and parsed successfully",
            resume_id=resume.id,
            filename=file.filename,
            file_size=file.size or 0,
            parsed_sections=parsed_sections
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/api/create", response_model=ResumeResponse)
async def create_resume(
    resume_data: ResumeCreate,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create new resume from form data."""
    resume_service = ResumeService(db)
    
    resume = resume_service.create_resume_from_form(
        user_id=current_user.id,
        resume_data=resume_data.model_dump()
    )
    
    return ResumeResponse.model_validate(resume)


@router.put("/api/{resume_id}", response_model=ResumeResponse)
async def update_resume(
    resume_id: int,
    resume_data: ResumeUpdate,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update existing resume."""
    resume_service = ResumeService(db)
    
    resume = resume_service.update_resume(
        resume_id=resume_id,
        user_id=current_user.id,
        update_data=resume_data.model_dump(exclude_unset=True)
    )
    
    return ResumeResponse.model_validate(resume)


@router.delete("/api/{resume_id}")
async def delete_resume(
    resume_id: int,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete resume."""
    resume_service = ResumeService(db)
    
    success = resume_service.delete_resume(resume_id, current_user.id)
    
    if success:
        return {"message": "Resume deleted successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete resume"
        )
