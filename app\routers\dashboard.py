"""Dashboard router for main user dashboard."""

from fastapi import APIRouter, Depends, Request
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session

from app.database import get_db
from app.services.resume_service import ResumeService
from app.schemas.auth import UserResponse
from app.routers.auth import get_current_user

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")


@router.get("/", response_class=HTMLResponse)
async def dashboard(
    request: Request,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Main dashboard page."""
    resume_service = ResumeService(db)
    
    # Get user's resumes
    resumes = resume_service.get_user_resumes(current_user.id)
    
    # Calculate dashboard stats
    total_resumes = len(resumes)
    optimized_resumes = len([r for r in resumes if r.optimization_score is not None])
    avg_optimization_score = None
    avg_ats_score = None
    
    if optimized_resumes > 0:
        scores = [r.optimization_score for r in resumes if r.optimization_score is not None]
        avg_optimization_score = sum(scores) / len(scores)
        
        ats_scores = [r.ats_score for r in resumes if r.ats_score is not None]
        if ats_scores:
            avg_ats_score = sum(ats_scores) / len(ats_scores)
    
    # Get recent resumes (last 5)
    recent_resumes = resumes[:5]
    
    dashboard_data = {
        'total_resumes': total_resumes,
        'optimized_resumes': optimized_resumes,
        'avg_optimization_score': round(avg_optimization_score, 1) if avg_optimization_score else None,
        'avg_ats_score': round(avg_ats_score, 1) if avg_ats_score else None,
        'recent_resumes': recent_resumes
    }
    
    return templates.TemplateResponse(
        "dashboard/index.html",
        {
            "request": request, 
            "user": current_user, 
            "dashboard": dashboard_data
        }
    )
