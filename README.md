# Resume Optimizer

AI-powered resume optimization web application built with FastAPI, featuring ATS-friendly templates and job-description-tailored resume customization.

## Features

- 🤖 **AI-Powered Optimization** - Resume analysis and optimization using Google Gemini API
- 📄 **Resume Management** - Upload existing resumes or create new ones from scratch
- 🎯 **Job Description Analysis** - Analyze job postings and match them with your resume
- 📊 **ATS Compatibility** - Ensure your resume passes Applicant Tracking Systems
- 🔐 **User Authentication** - Secure JWT-based authentication system
- 📱 **Responsive Design** - Works on desktop and mobile devices

## Tech Stack

- **Backend:** FastAPI (Python)
- **Frontend:** Jinja2 + Bootstrap + HTMX + Alpine.js
- **Database:** SQLite with SQLAlchemy ORM
- **AI:** Google Gemini API
- **File Processing:** PyPDF2, python-docx
- **Authentication:** JWT with bcrypt

## Quick Start

1. **Install Dependencies**
   ```bash
   pip install -e .
   ```

2. **Set up Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Initialize Database**
   ```bash
   alembic upgrade head
   ```

4. **Run the Application**
   ```bash
   python main.py
   ```

5. **Visit the Application**
   Open http://localhost:8000 in your browser

## Configuration

### Environment Variables

- `SECRET_KEY` - JWT secret key (change in production)
- `GEMINI_API_KEY` - Google Gemini API key for AI features (optional)
- `DATABASE_URL` - Database connection string
- `MAX_FILE_SIZE` - Maximum upload file size in bytes

### AI Features

To enable AI-powered features:
1. Get a Google Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Add it to your `.env` file as `GEMINI_API_KEY=your-key-here`

## Usage

1. **Sign Up** - Create a new account
2. **Upload Resume** - Upload your existing resume (PDF/DOCX)
3. **Create Resume** - Build a new resume from scratch
4. **Analyze Jobs** - Paste job descriptions to get optimization suggestions
5. **Optimize** - Use AI to improve your resume for specific roles

## Development

### Project Structure
```
app/
├── main.py              # FastAPI application
├── config.py            # Configuration settings
├── database.py          # Database setup
├── models/              # SQLAlchemy models
├── routers/             # API routes
├── services/            # Business logic
├── utils/               # Utility functions
├── templates/           # Jinja2 templates
└── static/              # CSS, JS, images
```

### Running Tests
```bash
pip install -e ".[dev]"
pytest
```

## License

MIT License - see LICENSE file for details.