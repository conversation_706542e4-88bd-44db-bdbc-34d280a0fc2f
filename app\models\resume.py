"""Resume model for storing resume data and content."""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class Resume(Base):
    """Resume model for storing resume information and content."""
    
    __tablename__ = "resumes"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(255), nullable=False)
    
    # Resume content
    personal_info = Column(JSON, nullable=True)  # Name, contact info, etc.
    summary = Column(Text, nullable=True)
    work_experience = Column(JSON, nullable=True)  # Array of work experiences
    education = Column(JSON, nullable=True)  # Array of education entries
    skills = Column(JSON, nullable=True)  # Array of skills
    certifications = Column(JSON, nullable=True)  # Array of certifications
    projects = Column(JSON, nullable=True)  # Array of projects
    languages = Column(JSON, nullable=True)  # Array of languages
    additional_sections = Column(JSON, nullable=True)  # Custom sections
    
    # File information
    original_filename = Column(String(255), nullable=True)
    file_path = Column(String(500), nullable=True)
    file_type = Column(String(10), nullable=True)  # pdf, docx, etc.
    
    # Template and styling
    template_id = Column(Integer, ForeignKey("resume_templates.id"), nullable=True)
    custom_styling = Column(JSON, nullable=True)
    
    # Metadata
    is_active = Column(Boolean, default=True)
    is_public = Column(Boolean, default=False)
    version = Column(Integer, default=1)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # AI optimization data
    last_optimized_at = Column(DateTime(timezone=True), nullable=True)
    optimization_score = Column(Float, nullable=True)
    ats_score = Column(Float, nullable=True)
    keyword_density = Column(JSON, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="resumes")
    template = relationship("ResumeTemplate", back_populates="resumes")
    optimization_history = relationship("OptimizationHistory", back_populates="resume", cascade="all, delete-orphan")
    job_matches = relationship("JobMatch", back_populates="resume", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Resume(id={self.id}, title='{self.title}', user_id={self.user_id})>"


class ResumeTemplate(Base):
    """Resume template model for storing template designs."""
    
    __tablename__ = "resume_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)  # professional, creative, modern, etc.
    
    # Template files
    html_template = Column(Text, nullable=False)  # Jinja2 template
    css_styles = Column(Text, nullable=True)
    preview_image = Column(String(500), nullable=True)
    
    # Template metadata
    is_ats_friendly = Column(Boolean, default=True)
    is_active = Column(Boolean, default=True)
    is_premium = Column(Boolean, default=False)
    difficulty_level = Column(String(20), default="beginner")  # beginner, intermediate, advanced
    
    # Usage statistics
    usage_count = Column(Integer, default=0)
    rating = Column(Float, default=0.0)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    resumes = relationship("Resume", back_populates="template")
    
    def __repr__(self):
        return f"<ResumeTemplate(id={self.id}, name='{self.name}')>"
