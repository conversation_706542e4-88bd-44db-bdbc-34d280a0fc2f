{% extends "base.html" %}

{% block title %}Login - Resume Optimizer{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="card-title">Welcome Back</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>

                    <form id="loginForm" hx-post="/auth/api/login" hx-target="#loginResult" hx-swap="innerHTML">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                Remember me
                            </label>
                        </div>

                        <div id="loginResult" class="mb-3"></div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <span class="htmx-indicator spinner-border spinner-border-sm me-2" role="status"></span>
                            Sign In
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="mb-2">
                            <a href="#" class="text-decoration-none">Forgot your password?</a>
                        </p>
                        <p class="mb-0">
                            Don't have an account?
                            <a href="/auth/register" class="text-decoration-none">Sign up</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle successful login
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.target.id === 'loginResult') {
            const xhr = event.detail.xhr;
            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                if (response.access_token) {
                    // Store token in localStorage
                    localStorage.setItem('access_token', response.access_token);
                    // Redirect to dashboard
                    window.location.href = '/dashboard';
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        '<div class="alert alert-success">Login successful! Redirecting...</div>';
                }
            } else {
                const errorData = JSON.parse(xhr.responseText);
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-danger">${errorData.detail || 'Login failed'}</div>`;
            }
        }
    });
});
</script>
{% endblock %}
