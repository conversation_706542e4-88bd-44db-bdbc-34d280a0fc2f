"""Job description analyzer router."""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request, Form
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session

from app.database import get_db
from app.services.ai_service import AIService
from app.services.resume_service import ResumeService
from app.schemas.auth import UserResponse
from app.routers.auth import get_current_user, get_current_user_web
from app.config import settings

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")


@router.get("/", response_class=HTMLResponse)
async def analyzer_page(
    request: Request,
    resume_id: Optional[int] = None,
    current_user: UserResponse = Depends(get_current_user_web),
    db: Session = Depends(get_db)
):
    """Display job description analyzer page."""
    resume_service = ResumeService(db)
    
    # Get user's resumes for selection
    resumes = resume_service.get_user_resumes(current_user.id)
    
    # Get specific resume if ID provided
    selected_resume = None
    if resume_id:
        selected_resume = resume_service.get_resume_by_id(resume_id, current_user.id)
    
    return templates.TemplateResponse(
        "analyzer/index.html",
        {
            "request": request, 
            "user": current_user, 
            "resumes": resumes,
            "selected_resume": selected_resume
        }
    )


@router.post("/api/analyze-job")
async def analyze_job_description(
    job_description: str = Form(...),
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Analyze job description and extract key information."""
    if not job_description.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Job description cannot be empty"
        )
    
    ai_service = AIService(db)
    
    try:
        analysis = ai_service.analyze_job_description(
            job_description=job_description,
            user_id=current_user.id
        )
        
        if "error" in analysis:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=analysis["error"]
            )
        
        return {
            "message": "Job description analyzed successfully",
            "analysis": analysis
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Analysis failed: {str(e)}"
        )


@router.post("/api/match-resume")
async def match_resume_to_job(
    resume_id: int = Form(...),
    job_description: str = Form(...),
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Match resume against job description and provide optimization suggestions."""
    if not job_description.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Job description cannot be empty"
        )
    
    resume_service = ResumeService(db)
    ai_service = AIService(db)
    
    # Get resume
    resume = resume_service.get_resume_by_id(resume_id, current_user.id)
    if not resume:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Resume not found"
        )
    
    # Convert resume to text for analysis
    resume_text = _resume_to_text(resume)
    
    try:
        # Analyze job description
        job_analysis = ai_service.analyze_job_description(
            job_description=job_description,
            user_id=current_user.id
        )
        
        # Optimize resume for job
        optimization = ai_service.optimize_resume_for_job(
            resume_text=resume_text,
            job_description=job_description,
            user_id=current_user.id
        )
        
        if "error" in job_analysis or "error" in optimization:
            error_msg = job_analysis.get("error", "") or optimization.get("error", "")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=error_msg
            )
        
        return {
            "message": "Resume matched successfully",
            "resume_title": resume.title,
            "job_analysis": job_analysis,
            "optimization": optimization,
            "match_score": optimization.get("match_score", 0)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Matching failed: {str(e)}"
        )


@router.post("/api/optimize-resume")
async def optimize_resume(
    resume_id: int = Form(...),
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Optimize resume using AI analysis."""
    resume_service = ResumeService(db)
    ai_service = AIService(db)
    
    # Get resume
    resume = resume_service.get_resume_by_id(resume_id, current_user.id)
    if not resume:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Resume not found"
        )
    
    # Convert resume to text for analysis
    resume_text = _resume_to_text(resume)
    
    try:
        # Analyze resume
        analysis = ai_service.analyze_resume(
            resume_text=resume_text,
            user_id=current_user.id
        )
        
        if "error" in analysis:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=analysis["error"]
            )
        
        # Update resume with optimization scores
        resume_service.update_resume(
            resume_id=resume_id,
            user_id=current_user.id,
            update_data={
                "optimization_score": analysis.get("overall_score"),
                "ats_score": analysis.get("ats_score"),
                "last_optimized_at": "now()"
            }
        )
        
        return {
            "message": "Resume optimized successfully",
            "analysis": analysis
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Optimization failed: {str(e)}"
        )


def _resume_to_text(resume) -> str:
    """Convert resume object to text for AI analysis."""
    text_parts = []
    
    # Personal info
    if resume.personal_info:
        if resume.personal_info.get("name"):
            text_parts.append(f"Name: {resume.personal_info['name']}")
        if resume.personal_info.get("email"):
            text_parts.append(f"Email: {resume.personal_info['email']}")
        if resume.personal_info.get("phone"):
            text_parts.append(f"Phone: {resume.personal_info['phone']}")
        if resume.personal_info.get("location"):
            text_parts.append(f"Location: {resume.personal_info['location']}")
    
    # Summary
    if resume.summary:
        text_parts.append(f"\nPROFESSIONAL SUMMARY:\n{resume.summary}")
    
    # Work experience
    if resume.work_experience:
        text_parts.append("\nWORK EXPERIENCE:")
        for exp in resume.work_experience:
            if isinstance(exp, dict):
                company = exp.get("company", "")
                position = exp.get("position", "")
                description = exp.get("description", "")
                text_parts.append(f"\n{position} at {company}\n{description}")
    
    # Education
    if resume.education:
        text_parts.append("\nEDUCATION:")
        for edu in resume.education:
            if isinstance(edu, dict):
                degree = edu.get("degree", "")
                institution = edu.get("institution", "")
                text_parts.append(f"\n{degree} from {institution}")
    
    # Skills
    if resume.skills:
        skills_text = ", ".join(resume.skills) if isinstance(resume.skills, list) else str(resume.skills)
        text_parts.append(f"\nSKILLS:\n{skills_text}")
    
    # Certifications
    if resume.certifications:
        text_parts.append("\nCERTIFICATIONS:")
        for cert in resume.certifications:
            if isinstance(cert, dict):
                name = cert.get("name", "")
                issuer = cert.get("issuer", "")
                text_parts.append(f"\n{name} from {issuer}")
    
    # Projects
    if resume.projects:
        text_parts.append("\nPROJECTS:")
        for project in resume.projects:
            if isinstance(project, dict):
                name = project.get("name", "")
                description = project.get("description", "")
                text_parts.append(f"\n{name}: {description}")
    
    return "\n".join(text_parts)
