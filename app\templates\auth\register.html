{% extends "base.html" %}

{% block title %}Sign Up - Resume Optimizer{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="card-title">Create Account</h2>
                        <p class="text-muted">Join thousands of job seekers</p>
                    </div>

                    <form id="registerForm" hx-post="/auth/api/register" hx-target="#registerResult" hx-swap="innerHTML">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="firstName" name="first_name">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="lastName" name="last_name">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">
                                Password must be at least 8 characters with uppercase, lowercase, number, and special character.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> 
                                and <a href="#" class="text-decoration-none">Privacy Policy</a>
                            </label>
                        </div>

                        <div id="registerResult" class="mb-3"></div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <span class="htmx-indicator spinner-border spinner-border-sm me-2" role="status"></span>
                            Create Account
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="mb-0">
                            Already have an account?
                            <a href="/auth/login" class="text-decoration-none">Sign in</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registerForm');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');

    // Password confirmation validation
    function validatePasswords() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }

    password.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);

    // Handle form submission
    form.addEventListener('submit', function(e) {
        validatePasswords();
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Handle successful registration
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.target.id === 'registerResult') {
            const xhr = event.detail.xhr;
            if (xhr.status === 200) {
                document.getElementById('registerResult').innerHTML = 
                    '<div class="alert alert-success">Account created successfully! Please sign in.</div>';
                setTimeout(() => {
                    window.location.href = '/auth/login';
                }, 2000);
            } else {
                const errorData = JSON.parse(xhr.responseText);
                document.getElementById('registerResult').innerHTML = 
                    `<div class="alert alert-danger">${errorData.detail || 'Registration failed'}</div>`;
            }
        }
    });
});
</script>
{% endblock %}
