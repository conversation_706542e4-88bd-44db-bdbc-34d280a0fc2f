{% extends "base.html" %}

{% block title %}Profile - Resume Optimizer{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">User Profile</h4>
                </div>
                <div class="card-body">
                    <form id="profileForm" hx-put="/auth/api/profile" hx-target="#profileResult" hx-swap="innerHTML">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="firstName" name="first_name" 
                                       value="{{ user.first_name or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="lastName" name="last_name" 
                                       value="{{ user.last_name or '' }}">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" value="{{ user.email }}" disabled>
                            <div class="form-text">Email cannot be changed</div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ user.phone or '' }}">
                        </div>

                        <div class="mb-3">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   value="{{ user.location or '' }}" placeholder="City, State/Country">
                        </div>

                        <div class="mb-3">
                            <label for="linkedinUrl" class="form-label">LinkedIn URL</label>
                            <input type="url" class="form-control" id="linkedinUrl" name="linkedin_url" 
                                   value="{{ user.linkedin_url or '' }}" placeholder="https://linkedin.com/in/yourprofile">
                        </div>

                        <div class="mb-3">
                            <label for="websiteUrl" class="form-label">Website URL</label>
                            <input type="url" class="form-control" id="websiteUrl" name="website_url" 
                                   value="{{ user.website_url or '' }}" placeholder="https://yourwebsite.com">
                        </div>

                        <div class="mb-3">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea class="form-control" id="bio" name="bio" rows="4" 
                                      placeholder="Tell us about yourself...">{{ user.bio or '' }}</textarea>
                        </div>

                        <div id="profileResult" class="mb-3"></div>

                        <button type="submit" class="btn btn-primary">
                            <span class="htmx-indicator spinner-border spinner-border-sm me-2" role="status"></span>
                            Update Profile
                        </button>
                    </form>
                </div>
            </div>

            <!-- Password Change Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Change Password</h5>
                </div>
                <div class="card-body">
                    <form id="passwordForm" hx-post="/auth/api/change-password" hx-target="#passwordResult" hx-swap="innerHTML">
                        <div class="mb-3">
                            <label for="currentPassword" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="currentPassword" name="current_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="newPassword" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="newPassword" name="new_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="confirmNewPassword" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirmNewPassword" required>
                        </div>

                        <div id="passwordResult" class="mb-3"></div>

                        <button type="submit" class="btn btn-warning">
                            <span class="htmx-indicator spinner-border spinner-border-sm me-2" role="status"></span>
                            Change Password
                        </button>
                    </form>
                </div>
            </div>

            <!-- Account Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Account Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Account Status:</strong> 
                                <span class="badge bg-{{ 'success' if user.is_active else 'danger' }}">
                                    {{ 'Active' if user.is_active else 'Inactive' }}
                                </span>
                            </p>
                            <p><strong>Email Verified:</strong> 
                                <span class="badge bg-{{ 'success' if user.is_verified else 'warning' }}">
                                    {{ 'Verified' if user.is_verified else 'Pending' }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Member Since:</strong> {{ user.created_at.strftime('%B %d, %Y') }}</p>
                            {% if user.updated_at %}
                            <p><strong>Last Updated:</strong> {{ user.updated_at.strftime('%B %d, %Y') }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const newPassword = document.getElementById('newPassword');
    const confirmNewPassword = document.getElementById('confirmNewPassword');

    function validatePasswords() {
        if (newPassword.value !== confirmNewPassword.value) {
            confirmNewPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmNewPassword.setCustomValidity('');
        }
    }

    newPassword.addEventListener('input', validatePasswords);
    confirmNewPassword.addEventListener('input', validatePasswords);

    // Handle successful updates
    document.body.addEventListener('htmx:afterRequest', function(event) {
        const xhr = event.detail.xhr;
        
        if (event.detail.target.id === 'profileResult') {
            if (xhr.status === 200) {
                document.getElementById('profileResult').innerHTML = 
                    '<div class="alert alert-success">Profile updated successfully!</div>';
            } else {
                const errorData = JSON.parse(xhr.responseText);
                document.getElementById('profileResult').innerHTML = 
                    `<div class="alert alert-danger">${errorData.detail || 'Update failed'}</div>`;
            }
        }
        
        if (event.detail.target.id === 'passwordResult') {
            if (xhr.status === 200) {
                document.getElementById('passwordResult').innerHTML = 
                    '<div class="alert alert-success">Password changed successfully!</div>';
                document.getElementById('passwordForm').reset();
            } else {
                const errorData = JSON.parse(xhr.responseText);
                document.getElementById('passwordResult').innerHTML = 
                    `<div class="alert alert-danger">${errorData.detail || 'Password change failed'}</div>`;
            }
        }
    });
});
</script>
{% endblock %}
