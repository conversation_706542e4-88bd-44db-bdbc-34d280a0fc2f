"""Pydantic schemas for resume operations."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class PersonalInfo(BaseModel):
    """Personal information schema."""
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None
    linkedin_url: Optional[str] = None
    website_url: Optional[str] = None
    urls: Optional[List[str]] = []


class WorkExperience(BaseModel):
    """Work experience schema."""
    company: str
    position: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    current: bool = False
    location: Optional[str] = None
    description: Optional[str] = None
    achievements: Optional[List[str]] = []


class Education(BaseModel):
    """Education schema."""
    institution: str
    degree: str
    field: Optional[str] = None
    graduation_date: Optional[str] = None
    gpa: Optional[str] = None
    honors: Optional[List[str]] = []


class Certification(BaseModel):
    """Certification schema."""
    name: str
    issuer: str
    date: Optional[str] = None
    expiry_date: Optional[str] = None
    credential_id: Optional[str] = None
    url: Optional[str] = None


class Project(BaseModel):
    """Project schema."""
    name: str
    description: str
    technologies: Optional[List[str]] = []
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    url: Optional[str] = None
    github_url: Optional[str] = None


class Language(BaseModel):
    """Language schema."""
    name: str
    proficiency: str  # Native, Fluent, Intermediate, Basic


class ResumeCreate(BaseModel):
    """Schema for creating a new resume."""
    title: str = Field(..., min_length=1, max_length=255)
    personal_info: Optional[PersonalInfo] = None
    summary: Optional[str] = None
    work_experience: Optional[List[WorkExperience]] = []
    education: Optional[List[Education]] = []
    skills: Optional[List[str]] = []
    certifications: Optional[List[Certification]] = []
    projects: Optional[List[Project]] = []
    languages: Optional[List[Language]] = []
    additional_sections: Optional[Dict[str, Any]] = {}
    template_id: Optional[int] = None


class ResumeUpdate(BaseModel):
    """Schema for updating a resume."""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    personal_info: Optional[PersonalInfo] = None
    summary: Optional[str] = None
    work_experience: Optional[List[WorkExperience]] = None
    education: Optional[List[Education]] = None
    skills: Optional[List[str]] = None
    certifications: Optional[List[Certification]] = None
    projects: Optional[List[Project]] = None
    languages: Optional[List[Language]] = None
    additional_sections: Optional[Dict[str, Any]] = None
    template_id: Optional[int] = None
    custom_styling: Optional[Dict[str, Any]] = None


class ResumeResponse(BaseModel):
    """Schema for resume response."""
    id: int
    user_id: int
    title: str
    personal_info: Optional[Dict[str, Any]] = None
    summary: Optional[str] = None
    work_experience: Optional[List[Dict[str, Any]]] = None
    education: Optional[List[Dict[str, Any]]] = None
    skills: Optional[List[str]] = None
    certifications: Optional[List[Dict[str, Any]]] = None
    projects: Optional[List[Dict[str, Any]]] = None
    languages: Optional[List[Dict[str, Any]]] = None
    additional_sections: Optional[Dict[str, Any]] = None
    
    # File information
    original_filename: Optional[str] = None
    file_type: Optional[str] = None
    
    # Template and styling
    template_id: Optional[int] = None
    custom_styling: Optional[Dict[str, Any]] = None
    
    # Metadata
    is_active: bool
    is_public: bool
    version: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # AI optimization data
    last_optimized_at: Optional[datetime] = None
    optimization_score: Optional[float] = None
    ats_score: Optional[float] = None
    keyword_density: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True


class ResumeListResponse(BaseModel):
    """Schema for resume list response."""
    id: int
    title: str
    original_filename: Optional[str] = None
    file_type: Optional[str] = None
    version: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    optimization_score: Optional[float] = None
    ats_score: Optional[float] = None
    
    class Config:
        from_attributes = True


class FileUploadResponse(BaseModel):
    """Schema for file upload response."""
    message: str
    resume_id: int
    filename: str
    file_size: int
    parsed_sections: List[str]


class ResumeTemplateResponse(BaseModel):
    """Schema for resume template response."""
    id: int
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    preview_image: Optional[str] = None
    is_ats_friendly: bool
    is_premium: bool
    difficulty_level: str
    usage_count: int
    rating: float
    
    class Config:
        from_attributes = True
