{% extends "base.html" %}

{% block title %}Create Resume - Resume Optimizer{% endblock %}

{% block extra_head %}
<style>
.form-section {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background-color: #f8f9fa;
}

.section-header {
    display: flex;
    justify-content-between;
    align-items-center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.dynamic-section {
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
}

.remove-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Create New Resume</h4>
                    <small class="text-muted">Build your professional resume from scratch</small>
                </div>
                <div class="card-body">
                    <form id="createResumeForm" hx-post="/resumes/api/create" hx-target="#createResult" hx-swap="innerHTML">
                        
                        <!-- Resume Title -->
                        <div class="form-section">
                            <div class="section-header">
                                <h5 class="mb-0">Resume Title</h5>
                            </div>
                            <div class="mb-3">
                                <input type="text" class="form-control" name="title" required 
                                       placeholder="e.g., Software Engineer Resume">
                            </div>
                        </div>

                        <!-- Personal Information -->
                        <div class="form-section">
                            <div class="section-header">
                                <h5 class="mb-0">Personal Information</h5>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" name="personal_info.name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" name="personal_info.email" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Phone</label>
                                    <input type="tel" class="form-control" name="personal_info.phone">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Location</label>
                                    <input type="text" class="form-control" name="personal_info.location" 
                                           placeholder="City, State">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">LinkedIn URL</label>
                                    <input type="url" class="form-control" name="personal_info.linkedin_url">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Website URL</label>
                                    <input type="url" class="form-control" name="personal_info.website_url">
                                </div>
                            </div>
                        </div>

                        <!-- Professional Summary -->
                        <div class="form-section">
                            <div class="section-header">
                                <h5 class="mb-0">Professional Summary</h5>
                            </div>
                            <div class="mb-3">
                                <textarea class="form-control" name="summary" rows="4" 
                                          placeholder="Write a compelling summary of your professional background and key achievements..."></textarea>
                                <div class="form-text">2-3 sentences highlighting your experience and value proposition</div>
                            </div>
                        </div>

                        <!-- Work Experience -->
                        <div class="form-section">
                            <div class="section-header">
                                <h5 class="mb-0">Work Experience</h5>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="addExperience">
                                    <i class="fas fa-plus me-1"></i>Add Experience
                                </button>
                            </div>
                            <div id="experienceContainer">
                                <!-- Experience entries will be added here -->
                            </div>
                        </div>

                        <!-- Education -->
                        <div class="form-section">
                            <div class="section-header">
                                <h5 class="mb-0">Education</h5>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="addEducation">
                                    <i class="fas fa-plus me-1"></i>Add Education
                                </button>
                            </div>
                            <div id="educationContainer">
                                <!-- Education entries will be added here -->
                            </div>
                        </div>

                        <!-- Skills -->
                        <div class="form-section">
                            <div class="section-header">
                                <h5 class="mb-0">Skills</h5>
                            </div>
                            <div class="mb-3">
                                <textarea class="form-control" name="skills" rows="3" 
                                          placeholder="Enter your skills separated by commas (e.g., JavaScript, Python, Project Management)"></textarea>
                                <div class="form-text">Separate skills with commas</div>
                            </div>
                        </div>

                        <!-- Certifications -->
                        <div class="form-section">
                            <div class="section-header">
                                <h5 class="mb-0">Certifications</h5>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="addCertification">
                                    <i class="fas fa-plus me-1"></i>Add Certification
                                </button>
                            </div>
                            <div id="certificationContainer">
                                <!-- Certification entries will be added here -->
                            </div>
                        </div>

                        <!-- Projects -->
                        <div class="form-section">
                            <div class="section-header">
                                <h5 class="mb-0">Projects</h5>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="addProject">
                                    <i class="fas fa-plus me-1"></i>Add Project
                                </button>
                            </div>
                            <div id="projectContainer">
                                <!-- Project entries will be added here -->
                            </div>
                        </div>

                        <div id="createResult" class="mb-3"></div>

                        <div class="d-flex justify-content-between">
                            <a href="/resumes" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Resumes
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <span class="htmx-indicator spinner-border spinner-border-sm me-2" role="status"></span>
                                <i class="fas fa-save me-2"></i>Create Resume
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Templates for dynamic sections -->
<template id="experienceTemplate">
    <div class="dynamic-section position-relative">
        <button type="button" class="btn btn-outline-danger btn-sm remove-btn remove-experience">
            <i class="fas fa-times"></i>
        </button>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">Company *</label>
                <input type="text" class="form-control" name="work_experience[INDEX].company" required>
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">Position *</label>
                <input type="text" class="form-control" name="work_experience[INDEX].position" required>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label class="form-label">Start Date</label>
                <input type="text" class="form-control" name="work_experience[INDEX].start_date" 
                       placeholder="MM/YYYY">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">End Date</label>
                <input type="text" class="form-control" name="work_experience[INDEX].end_date" 
                       placeholder="MM/YYYY or Present">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">Location</label>
                <input type="text" class="form-control" name="work_experience[INDEX].location">
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">Description</label>
            <textarea class="form-control" name="work_experience[INDEX].description" rows="3" 
                      placeholder="Describe your responsibilities and achievements..."></textarea>
        </div>
    </div>
</template>

<template id="educationTemplate">
    <div class="dynamic-section position-relative">
        <button type="button" class="btn btn-outline-danger btn-sm remove-btn remove-education">
            <i class="fas fa-times"></i>
        </button>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">Institution *</label>
                <input type="text" class="form-control" name="education[INDEX].institution" required>
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">Degree *</label>
                <input type="text" class="form-control" name="education[INDEX].degree" required>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">Field of Study</label>
                <input type="text" class="form-control" name="education[INDEX].field">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">Graduation Date</label>
                <input type="text" class="form-control" name="education[INDEX].graduation_date" 
                       placeholder="MM/YYYY">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">GPA</label>
                <input type="text" class="form-control" name="education[INDEX].gpa" placeholder="3.8/4.0">
            </div>
        </div>
    </div>
</template>

<template id="certificationTemplate">
    <div class="dynamic-section position-relative">
        <button type="button" class="btn btn-outline-danger btn-sm remove-btn remove-certification">
            <i class="fas fa-times"></i>
        </button>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">Certification Name *</label>
                <input type="text" class="form-control" name="certifications[INDEX].name" required>
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">Issuing Organization *</label>
                <input type="text" class="form-control" name="certifications[INDEX].issuer" required>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label class="form-label">Date Obtained</label>
                <input type="text" class="form-control" name="certifications[INDEX].date" 
                       placeholder="MM/YYYY">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">Expiry Date</label>
                <input type="text" class="form-control" name="certifications[INDEX].expiry_date" 
                       placeholder="MM/YYYY">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">Credential ID</label>
                <input type="text" class="form-control" name="certifications[INDEX].credential_id">
            </div>
        </div>
    </div>
</template>

<template id="projectTemplate">
    <div class="dynamic-section position-relative">
        <button type="button" class="btn btn-outline-danger btn-sm remove-btn remove-project">
            <i class="fas fa-times"></i>
        </button>
        <div class="row">
            <div class="col-md-12 mb-3">
                <label class="form-label">Project Name *</label>
                <input type="text" class="form-control" name="projects[INDEX].name" required>
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">Description *</label>
            <textarea class="form-control" name="projects[INDEX].description" rows="3" required 
                      placeholder="Describe the project, your role, and key achievements..."></textarea>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">Technologies Used</label>
                <input type="text" class="form-control" name="projects[INDEX].technologies" 
                       placeholder="React, Node.js, MongoDB">
                <div class="form-text">Separate with commas</div>
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">Project URL</label>
                <input type="url" class="form-control" name="projects[INDEX].url">
            </div>
        </div>
    </div>
</template>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let experienceIndex = 0;
    let educationIndex = 0;
    let certificationIndex = 0;
    let projectIndex = 0;

    // Add initial entries
    addExperienceEntry();
    addEducationEntry();

    // Add experience entry
    document.getElementById('addExperience').addEventListener('click', addExperienceEntry);
    
    function addExperienceEntry() {
        const template = document.getElementById('experienceTemplate');
        const container = document.getElementById('experienceContainer');
        const clone = template.content.cloneNode(true);
        
        // Replace INDEX with actual index
        clone.innerHTML = clone.innerHTML.replace(/INDEX/g, experienceIndex);
        container.appendChild(clone);
        
        // Add remove handler
        container.lastElementChild.querySelector('.remove-experience').addEventListener('click', function() {
            this.closest('.dynamic-section').remove();
        });
        
        experienceIndex++;
    }

    // Add education entry
    document.getElementById('addEducation').addEventListener('click', addEducationEntry);
    
    function addEducationEntry() {
        const template = document.getElementById('educationTemplate');
        const container = document.getElementById('educationContainer');
        const clone = template.content.cloneNode(true);
        
        clone.innerHTML = clone.innerHTML.replace(/INDEX/g, educationIndex);
        container.appendChild(clone);
        
        container.lastElementChild.querySelector('.remove-education').addEventListener('click', function() {
            this.closest('.dynamic-section').remove();
        });
        
        educationIndex++;
    }

    // Add certification entry
    document.getElementById('addCertification').addEventListener('click', function() {
        const template = document.getElementById('certificationTemplate');
        const container = document.getElementById('certificationContainer');
        const clone = template.content.cloneNode(true);
        
        clone.innerHTML = clone.innerHTML.replace(/INDEX/g, certificationIndex);
        container.appendChild(clone);
        
        container.lastElementChild.querySelector('.remove-certification').addEventListener('click', function() {
            this.closest('.dynamic-section').remove();
        });
        
        certificationIndex++;
    });

    // Add project entry
    document.getElementById('addProject').addEventListener('click', function() {
        const template = document.getElementById('projectTemplate');
        const container = document.getElementById('projectContainer');
        const clone = template.content.cloneNode(true);
        
        clone.innerHTML = clone.innerHTML.replace(/INDEX/g, projectIndex);
        container.appendChild(clone);
        
        container.lastElementChild.querySelector('.remove-project').addEventListener('click', function() {
            this.closest('.dynamic-section').remove();
        });
        
        projectIndex++;
    });

    // Handle form submission
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.target.id === 'createResult') {
            const xhr = event.detail.xhr;
            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                document.getElementById('createResult').innerHTML = `
                    <div class="alert alert-success">
                        <h6>Resume Created Successfully!</h6>
                        <p class="mb-2">Your resume has been created and saved.</p>
                        <a href="/resumes/${response.id}" class="btn btn-primary btn-sm">View Resume</a>
                    </div>
                `;
            } else {
                const errorData = JSON.parse(xhr.responseText);
                document.getElementById('createResult').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Creation Failed:</strong> ${errorData.detail || 'Unknown error occurred'}
                    </div>
                `;
            }
        }
    });
});
</script>
{% endblock %}
