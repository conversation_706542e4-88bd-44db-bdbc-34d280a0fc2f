"""Application configuration settings."""

from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings."""
    
    # App settings
    app_name: str = "Resume Optimizer"
    debug: bool = False
    version: str = "0.1.0"
    
    # Database settings
    database_url: str = "sqlite:///./resume_optimizer.db"
    
    # Security settings
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # AI API settings
    gemini_api_key: Optional[str] = None
    
    # File upload settings
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    upload_dir: str = "uploads"
    allowed_extensions: set = {".pdf", ".docx", ".doc"}
    
    # Template settings
    templates_dir: str = "app/templates"
    static_dir: str = "app/static"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
