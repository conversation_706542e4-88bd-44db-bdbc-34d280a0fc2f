"""AI service for resume optimization and job analysis using Google Gemini."""

import json
import time
from typing import Dict, List, Optional, Tuple
import google.generativeai as genai
from sqlalchemy.orm import Session

from app.config import settings
from app.models.optimization import AIInteraction
from app.models.resume import Resume
import logging

logger = logging.getLogger(__name__)


class AIService:
    """Service for AI-powered resume optimization and analysis."""
    
    def __init__(self, db: Session):
        self.db = db
        if settings.gemini_api_key:
            genai.configure(api_key=settings.gemini_api_key)
            self.model = genai.GenerativeModel('gemini-pro')
        else:
            self.model = None
            logger.warning("Gemini API key not configured")
    
    def _log_interaction(
        self, 
        user_id: Optional[int], 
        interaction_type: str, 
        prompt: str, 
        response: str, 
        processing_time: float,
        error: Optional[str] = None
    ) -> AIInteraction:
        """Log AI interaction to database."""
        interaction = AIInteraction(
            user_id=user_id,
            interaction_type=interaction_type,
            ai_provider="gemini",
            model_name="gemini-pro",
            prompt=prompt,
            response=response if not error else None,
            processing_time=processing_time,
            status="completed" if not error else "failed",
            error_message=error
        )
        
        self.db.add(interaction)
        self.db.commit()
        return interaction
    
    def analyze_resume(self, resume_text: str, user_id: Optional[int] = None) -> Dict:
        """Analyze resume and provide optimization suggestions."""
        if not self.model:
            return {"error": "AI service not available"}
        
        prompt = f"""
        Analyze the following resume and provide detailed feedback:

        RESUME:
        {resume_text}

        Please provide a JSON response with the following structure:
        {{
            "overall_score": <score from 0-100>,
            "ats_score": <ATS compatibility score from 0-100>,
            "strengths": [<list of strengths>],
            "weaknesses": [<list of areas for improvement>],
            "keyword_suggestions": [<list of relevant keywords to add>],
            "formatting_suggestions": [<list of formatting improvements>],
            "content_suggestions": [<list of content improvements>],
            "section_analysis": {{
                "summary": <analysis of professional summary>,
                "experience": <analysis of work experience>,
                "education": <analysis of education section>,
                "skills": <analysis of skills section>
            }}
        }}

        Focus on:
        1. ATS (Applicant Tracking System) compatibility
        2. Keyword optimization
        3. Content quality and relevance
        4. Structure and formatting
        5. Quantifiable achievements
        """
        
        start_time = time.time()
        try:
            response = self.model.generate_content(prompt)
            processing_time = time.time() - start_time
            
            # Extract JSON from response
            response_text = response.text
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                json_text = response_text[json_start:json_end].strip()
            else:
                json_text = response_text
            
            try:
                result = json.loads(json_text)
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                result = {
                    "overall_score": 70,
                    "ats_score": 65,
                    "analysis": response_text,
                    "error": "Could not parse structured response"
                }
            
            self._log_interaction(
                user_id, "resume_analysis", prompt, response_text, processing_time
            )
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            logger.error(f"Resume analysis failed: {error_msg}")
            
            self._log_interaction(
                user_id, "resume_analysis", prompt, "", processing_time, error_msg
            )
            
            return {"error": f"Analysis failed: {error_msg}"}
    
    def analyze_job_description(self, job_description: str, user_id: Optional[int] = None) -> Dict:
        """Analyze job description and extract key requirements."""
        if not self.model:
            return {"error": "AI service not available"}
        
        prompt = f"""
        Analyze the following job description and extract key information:

        JOB DESCRIPTION:
        {job_description}

        Please provide a JSON response with the following structure:
        {{
            "job_title": "<extracted job title>",
            "required_skills": [<list of required skills>],
            "preferred_skills": [<list of preferred skills>],
            "required_experience": "<experience level required>",
            "education_requirements": [<list of education requirements>],
            "key_responsibilities": [<list of main responsibilities>],
            "important_keywords": [<list of important keywords for ATS>],
            "company_culture_keywords": [<keywords related to company culture>],
            "technical_requirements": [<specific technical requirements>],
            "soft_skills": [<required soft skills>],
            "certifications": [<mentioned certifications>]
        }}

        Focus on extracting:
        1. Hard and soft skills
        2. Technical requirements
        3. Experience level
        4. Education requirements
        5. Important keywords for ATS optimization
        6. Certifications or licenses mentioned
        """
        
        start_time = time.time()
        try:
            response = self.model.generate_content(prompt)
            processing_time = time.time() - start_time
            
            # Extract JSON from response
            response_text = response.text
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                json_text = response_text[json_start:json_end].strip()
            else:
                json_text = response_text
            
            try:
                result = json.loads(json_text)
            except json.JSONDecodeError:
                result = {
                    "analysis": response_text,
                    "error": "Could not parse structured response"
                }
            
            self._log_interaction(
                user_id, "job_analysis", prompt, response_text, processing_time
            )
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            logger.error(f"Job description analysis failed: {error_msg}")
            
            self._log_interaction(
                user_id, "job_analysis", prompt, "", processing_time, error_msg
            )
            
            return {"error": f"Analysis failed: {error_msg}"}
    
    def optimize_resume_for_job(
        self, 
        resume_text: str, 
        job_description: str, 
        user_id: Optional[int] = None
    ) -> Dict:
        """Optimize resume content for a specific job description."""
        if not self.model:
            return {"error": "AI service not available"}
        
        prompt = f"""
        Optimize the following resume for the given job description:

        RESUME:
        {resume_text}

        JOB DESCRIPTION:
        {job_description}

        Please provide a JSON response with optimization suggestions:
        {{
            "match_score": <how well resume matches job from 0-100>,
            "missing_keywords": [<keywords from job description missing in resume>],
            "keyword_suggestions": [<specific keywords to add and where>],
            "content_modifications": [<specific content changes to make>],
            "skills_to_emphasize": [<skills to highlight more prominently>],
            "experience_adjustments": [<how to adjust experience descriptions>],
            "summary_optimization": "<optimized professional summary>",
            "priority_changes": [<most important changes to make first>],
            "ats_improvements": [<specific ATS optimization suggestions>]
        }}

        Focus on:
        1. Keyword alignment with job requirements
        2. Highlighting relevant experience
        3. Emphasizing matching skills
        4. Optimizing for ATS systems
        5. Maintaining authenticity while improving match
        """
        
        start_time = time.time()
        try:
            response = self.model.generate_content(prompt)
            processing_time = time.time() - start_time
            
            # Extract JSON from response
            response_text = response.text
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                json_text = response_text[json_start:json_end].strip()
            else:
                json_text = response_text
            
            try:
                result = json.loads(json_text)
            except json.JSONDecodeError:
                result = {
                    "optimization": response_text,
                    "error": "Could not parse structured response"
                }
            
            self._log_interaction(
                user_id, "resume_optimization", prompt, response_text, processing_time
            )
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            logger.error(f"Resume optimization failed: {error_msg}")
            
            self._log_interaction(
                user_id, "resume_optimization", prompt, "", processing_time, error_msg
            )
            
            return {"error": f"Optimization failed: {error_msg}"}
    
    def generate_resume_summary(
        self, 
        experience_data: List[Dict], 
        skills: List[str], 
        target_role: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> str:
        """Generate an optimized professional summary."""
        if not self.model:
            return "AI service not available"
        
        experience_text = "\n".join([
            f"- {exp.get('position', '')} at {exp.get('company', '')}: {exp.get('description', '')}"
            for exp in experience_data
        ])
        
        skills_text = ", ".join(skills)
        
        prompt = f"""
        Generate a compelling professional summary based on the following information:

        WORK EXPERIENCE:
        {experience_text}

        SKILLS:
        {skills_text}

        TARGET ROLE: {target_role or "General professional role"}

        Create a 2-3 sentence professional summary that:
        1. Highlights key experience and achievements
        2. Emphasizes relevant skills
        3. Shows value proposition to employers
        4. Is optimized for ATS systems
        5. Is tailored to the target role if specified

        Return only the summary text, no additional formatting or explanation.
        """
        
        start_time = time.time()
        try:
            response = self.model.generate_content(prompt)
            processing_time = time.time() - start_time
            
            summary = response.text.strip()
            
            self._log_interaction(
                user_id, "summary_generation", prompt, summary, processing_time
            )
            
            return summary
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            logger.error(f"Summary generation failed: {error_msg}")
            
            self._log_interaction(
                user_id, "summary_generation", prompt, "", processing_time, error_msg
            )
            
            return f"Error generating summary: {error_msg}"
