{% extends "base.html" %}

{% block title %}Job Description Analyzer - Resume Optimizer{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Job Description Analyzer</h4>
                    <small class="text-muted">Analyze job descriptions and optimize your resume for better matches</small>
                </div>
                <div class="card-body">
                    <!-- Job Description Input -->
                    <div class="mb-4">
                        <label for="jobDescription" class="form-label">Job Description *</label>
                        <textarea class="form-control" id="jobDescription" rows="8" 
                                  placeholder="Paste the job description here..."></textarea>
                        <div class="form-text">Copy and paste the complete job description from the job posting</div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-primary w-100" id="analyzeJobBtn"
                                    hx-post="/analyzer/api/analyze-job"
                                    hx-include="#jobDescription"
                                    hx-target="#jobAnalysisResult"
                                    hx-swap="innerHTML">
                                <span class="htmx-indicator spinner-border spinner-border-sm me-2" role="status"></span>
                                <i class="fas fa-search me-2"></i>Analyze Job Description
                            </button>
                        </div>
                        <div class="col-md-6">
                            {% if resumes %}
                            <div class="dropdown w-100">
                                <button class="btn btn-primary dropdown-toggle w-100" type="button" 
                                        data-bs-toggle="dropdown" id="matchResumeBtn">
                                    <i class="fas fa-magic me-2"></i>Match with Resume
                                </button>
                                <ul class="dropdown-menu w-100">
                                    {% for resume in resumes %}
                                    <li>
                                        <a class="dropdown-item match-resume-item" href="#" 
                                           data-resume-id="{{ resume.id }}" data-resume-title="{{ resume.title }}">
                                            {{ resume.title }}
                                        </a>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% else %}
                            <a href="/resumes/create" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-plus me-2"></i>Create Resume First
                            </a>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Results Area -->
                    <div id="jobAnalysisResult"></div>
                    <div id="resumeMatchResult"></div>
                </div>
            </div>

            <!-- Tips Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">How to Use the Analyzer</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-search me-2"></i>Job Analysis
                            </h6>
                            <ul class="list-unstyled small">
                                <li class="mb-1">• Extract key skills and requirements</li>
                                <li class="mb-1">• Identify important keywords for ATS</li>
                                <li class="mb-1">• Understand experience level needed</li>
                                <li class="mb-1">• Find education and certification requirements</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">
                                <i class="fas fa-magic me-2"></i>Resume Matching
                            </h6>
                            <ul class="list-unstyled small">
                                <li class="mb-1">• Get a match score for your resume</li>
                                <li class="mb-1">• Find missing keywords to add</li>
                                <li class="mb-1">• Receive specific optimization suggestions</li>
                                <li class="mb-1">• Improve ATS compatibility</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const jobDescription = document.getElementById('jobDescription');
    const analyzeJobBtn = document.getElementById('analyzeJobBtn');
    
    // Handle job description analysis
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.target.id === 'jobAnalysisResult') {
            const xhr = event.detail.xhr;
            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                displayJobAnalysis(response.analysis);
            } else {
                const errorData = JSON.parse(xhr.responseText);
                document.getElementById('jobAnalysisResult').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Analysis Failed:</strong> ${errorData.detail || 'Unknown error occurred'}
                    </div>
                `;
            }
        }
        
        if (event.detail.target.id === 'resumeMatchResult') {
            const xhr = event.detail.xhr;
            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                displayResumeMatch(response);
            } else {
                const errorData = JSON.parse(xhr.responseText);
                document.getElementById('resumeMatchResult').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Matching Failed:</strong> ${errorData.detail || 'Unknown error occurred'}
                    </div>
                `;
            }
        }
    });

    // Handle resume matching
    document.querySelectorAll('.match-resume-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            const resumeId = this.dataset.resumeId;
            const resumeTitle = this.dataset.resumeTitle;
            const jobDesc = jobDescription.value.trim();
            
            if (!jobDesc) {
                alert('Please enter a job description first');
                return;
            }
            
            // Create form data
            const formData = new FormData();
            formData.append('resume_id', resumeId);
            formData.append('job_description', jobDesc);
            
            // Show loading
            document.getElementById('resumeMatchResult').innerHTML = `
                <div class="alert alert-info">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                        <div>Matching "${resumeTitle}" with job description...</div>
                    </div>
                </div>
            `;
            
            // Make request
            fetch('/analyzer/api/match-resume', {
                method: 'POST',
                body: formData,
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    displayResumeMatch(data);
                } else {
                    throw new Error(data.detail || 'Unknown error');
                }
            })
            .catch(error => {
                document.getElementById('resumeMatchResult').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Matching Failed:</strong> ${error.message}
                    </div>
                `;
            });
        });
    });

    function displayJobAnalysis(analysis) {
        let html = `
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Job Analysis Results</h5>
                </div>
                <div class="card-body">
        `;
        
        if (analysis.job_title) {
            html += `<h6 class="text-primary">${analysis.job_title}</h6>`;
        }
        
        if (analysis.required_skills && analysis.required_skills.length > 0) {
            html += `
                <div class="mb-3">
                    <strong>Required Skills:</strong>
                    <div class="mt-2">
                        ${analysis.required_skills.map(skill => `<span class="badge bg-danger me-1 mb-1">${skill}</span>`).join('')}
                    </div>
                </div>
            `;
        }
        
        if (analysis.preferred_skills && analysis.preferred_skills.length > 0) {
            html += `
                <div class="mb-3">
                    <strong>Preferred Skills:</strong>
                    <div class="mt-2">
                        ${analysis.preferred_skills.map(skill => `<span class="badge bg-warning me-1 mb-1">${skill}</span>`).join('')}
                    </div>
                </div>
            `;
        }
        
        if (analysis.important_keywords && analysis.important_keywords.length > 0) {
            html += `
                <div class="mb-3">
                    <strong>Important Keywords for ATS:</strong>
                    <div class="mt-2">
                        ${analysis.important_keywords.map(keyword => `<span class="badge bg-info me-1 mb-1">${keyword}</span>`).join('')}
                    </div>
                </div>
            `;
        }
        
        if (analysis.required_experience) {
            html += `<p><strong>Experience Level:</strong> ${analysis.required_experience}</p>`;
        }
        
        html += `
                </div>
            </div>
        `;
        
        document.getElementById('jobAnalysisResult').innerHTML = html;
    }

    function displayResumeMatch(data) {
        const matchScore = data.match_score || 0;
        const scoreClass = matchScore >= 80 ? 'success' : matchScore >= 60 ? 'warning' : 'danger';
        
        let html = `
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Resume Match Results</h5>
                    <div class="score-circle score-${scoreClass >= 80 ? 'excellent' : scoreClass >= 60 ? 'good' : scoreClass >= 40 ? 'fair' : 'poor'}" style="width: 60px; height: 60px; font-size: 1rem;">
                        ${matchScore}%
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">Resume: ${data.resume_title}</h6>
        `;
        
        if (data.optimization && data.optimization.missing_keywords && data.optimization.missing_keywords.length > 0) {
            html += `
                <div class="mb-3">
                    <strong class="text-danger">Missing Keywords:</strong>
                    <div class="mt-2">
                        ${data.optimization.missing_keywords.map(keyword => `<span class="badge bg-danger me-1 mb-1">${keyword}</span>`).join('')}
                    </div>
                </div>
            `;
        }
        
        if (data.optimization && data.optimization.skills_to_emphasize && data.optimization.skills_to_emphasize.length > 0) {
            html += `
                <div class="mb-3">
                    <strong class="text-success">Skills to Emphasize:</strong>
                    <div class="mt-2">
                        ${data.optimization.skills_to_emphasize.map(skill => `<span class="badge bg-success me-1 mb-1">${skill}</span>`).join('')}
                    </div>
                </div>
            `;
        }
        
        if (data.optimization && data.optimization.priority_changes && data.optimization.priority_changes.length > 0) {
            html += `
                <div class="mb-3">
                    <strong>Priority Changes:</strong>
                    <ul class="mt-2">
                        ${data.optimization.priority_changes.map(change => `<li>${change}</li>`).join('')}
                    </ul>
                </div>
            `;
        }
        
        if (data.optimization && data.optimization.summary_optimization) {
            html += `
                <div class="mb-3">
                    <strong>Optimized Summary:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        ${data.optimization.summary_optimization}
                    </div>
                </div>
            `;
        }
        
        html += `
                </div>
            </div>
        `;
        
        document.getElementById('resumeMatchResult').innerHTML = html;
    }
});
</script>
{% endblock %}
