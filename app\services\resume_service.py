"""Resume service for managing resume operations."""

import os
import uuid
from typing import List, Optional, Dict, Any
from pathlib import Path
from sqlalchemy.orm import Session
from fastapi import HTTPException, status, UploadFile
import aiofiles

from app.models.resume import Resume, ResumeTemplate
from app.models.user import User
from app.utils.file_parser import ResumeParser, validate_file_size, validate_file_type, sanitize_filename
from app.config import settings


class ResumeService:
    """Service class for resume operations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.parser = ResumeParser()
    
    def get_user_resumes(self, user_id: int) -> List[Resume]:
        """Get all resumes for a user."""
        return self.db.query(Resume).filter(
            Resume.user_id == user_id,
            Resume.is_active == True
        ).order_by(Resume.updated_at.desc()).all()
    
    def get_resume_by_id(self, resume_id: int, user_id: int) -> Optional[Resume]:
        """Get resume by ID for a specific user."""
        return self.db.query(Resume).filter(
            Resume.id == resume_id,
            Resume.user_id == user_id,
            Resume.is_active == True
        ).first()
    
    async def save_uploaded_file(self, file: UploadFile, user_id: int) -> str:
        """Save uploaded file to disk and return file path."""
        # Validate file
        if not validate_file_type(file.filename):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported file type. Allowed: {', '.join(self.parser.supported_formats)}"
            )
        
        # Read file content to check size
        content = await file.read()
        if not validate_file_size(len(content)):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Maximum size: {settings.max_file_size // (1024*1024)}MB"
            )
        
        # Reset file pointer
        await file.seek(0)
        
        # Create user upload directory
        user_upload_dir = Path(settings.upload_dir) / str(user_id)
        user_upload_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate unique filename
        file_extension = Path(file.filename).suffix
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = user_upload_dir / unique_filename
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(content)
        
        return str(file_path)
    
    async def create_resume_from_upload(
        self, 
        file: UploadFile, 
        user_id: int, 
        title: Optional[str] = None
    ) -> Resume:
        """Create resume from uploaded file."""
        try:
            # Save file
            file_path = await self.save_uploaded_file(file, user_id)
            
            # Parse resume
            parsed_data = self.parser.parse_resume(file_path)
            
            # Create title if not provided
            if not title:
                title = f"Resume - {parsed_data['personal_info'].get('name', 'Untitled')}"
            
            # Create resume record
            resume = Resume(
                user_id=user_id,
                title=title,
                personal_info=parsed_data['personal_info'],
                summary=parsed_data['sections'].get('summary'),
                work_experience=self._parse_work_experience(parsed_data['sections'].get('experience', '')),
                education=self._parse_education(parsed_data['sections'].get('education', '')),
                skills=self._parse_skills(parsed_data['sections'].get('skills', '')),
                certifications=self._parse_certifications(parsed_data['sections'].get('certifications', '')),
                projects=self._parse_projects(parsed_data['sections'].get('projects', '')),
                original_filename=file.filename,
                file_path=file_path,
                file_type=Path(file.filename).suffix.lower().replace('.', ''),
                is_active=True
            )
            
            self.db.add(resume)
            self.db.commit()
            self.db.refresh(resume)
            
            return resume
            
        except Exception as e:
            # Clean up file if resume creation fails
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create resume: {str(e)}"
            )
    
    def create_resume_from_form(self, user_id: int, resume_data: Dict[str, Any]) -> Resume:
        """Create resume from form data."""
        resume = Resume(
            user_id=user_id,
            title=resume_data.get('title', 'New Resume'),
            personal_info=resume_data.get('personal_info', {}),
            summary=resume_data.get('summary'),
            work_experience=resume_data.get('work_experience', []),
            education=resume_data.get('education', []),
            skills=resume_data.get('skills', []),
            certifications=resume_data.get('certifications', []),
            projects=resume_data.get('projects', []),
            languages=resume_data.get('languages', []),
            additional_sections=resume_data.get('additional_sections', {}),
            template_id=resume_data.get('template_id'),
            is_active=True
        )
        
        self.db.add(resume)
        self.db.commit()
        self.db.refresh(resume)
        
        return resume
    
    def update_resume(self, resume_id: int, user_id: int, update_data: Dict[str, Any]) -> Resume:
        """Update existing resume."""
        resume = self.get_resume_by_id(resume_id, user_id)
        if not resume:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )
        
        # Update allowed fields
        allowed_fields = [
            'title', 'personal_info', 'summary', 'work_experience', 
            'education', 'skills', 'certifications', 'projects', 
            'languages', 'additional_sections', 'template_id', 'custom_styling'
        ]
        
        for field, value in update_data.items():
            if field in allowed_fields and hasattr(resume, field):
                setattr(resume, field, value)
        
        # Increment version
        resume.version += 1
        
        self.db.commit()
        self.db.refresh(resume)
        
        return resume
    
    def delete_resume(self, resume_id: int, user_id: int) -> bool:
        """Soft delete resume."""
        resume = self.get_resume_by_id(resume_id, user_id)
        if not resume:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )
        
        resume.is_active = False
        self.db.commit()
        
        return True
    
    def _parse_work_experience(self, experience_text: str) -> List[Dict]:
        """Parse work experience from text."""
        if not experience_text:
            return []
        
        # Simple parsing - can be enhanced with more sophisticated NLP
        experiences = []
        # Split by common patterns that indicate new job entries
        entries = experience_text.split('\n\n')
        
        for entry in entries:
            if entry.strip():
                experiences.append({
                    'raw_text': entry.strip(),
                    'company': '',
                    'position': '',
                    'start_date': '',
                    'end_date': '',
                    'description': entry.strip()
                })
        
        return experiences
    
    def _parse_education(self, education_text: str) -> List[Dict]:
        """Parse education from text."""
        if not education_text:
            return []
        
        education = []
        entries = education_text.split('\n\n')
        
        for entry in entries:
            if entry.strip():
                education.append({
                    'raw_text': entry.strip(),
                    'institution': '',
                    'degree': '',
                    'field': '',
                    'graduation_date': '',
                    'gpa': ''
                })
        
        return education
    
    def _parse_skills(self, skills_text: str) -> List[str]:
        """Parse skills from text."""
        if not skills_text:
            return []
        
        # Split by common delimiters
        skills = []
        for delimiter in [',', ';', '\n', '•', '·']:
            if delimiter in skills_text:
                skills = [skill.strip() for skill in skills_text.split(delimiter)]
                break
        
        if not skills:
            skills = [skills_text.strip()]
        
        return [skill for skill in skills if skill]
    
    def _parse_certifications(self, cert_text: str) -> List[Dict]:
        """Parse certifications from text."""
        if not cert_text:
            return []
        
        certifications = []
        entries = cert_text.split('\n')
        
        for entry in entries:
            if entry.strip():
                certifications.append({
                    'name': entry.strip(),
                    'issuer': '',
                    'date': '',
                    'credential_id': ''
                })
        
        return certifications
    
    def _parse_projects(self, projects_text: str) -> List[Dict]:
        """Parse projects from text."""
        if not projects_text:
            return []
        
        projects = []
        entries = projects_text.split('\n\n')
        
        for entry in entries:
            if entry.strip():
                projects.append({
                    'name': '',
                    'description': entry.strip(),
                    'technologies': [],
                    'url': ''
                })
        
        return projects
