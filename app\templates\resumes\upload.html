{% extends "base.html" %}

{% block title %}Upload Resume - Resume Optimizer{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Upload Your Resume</h4>
                    <small class="text-muted">Upload your existing resume to get started with AI optimization</small>
                </div>
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data" 
                          hx-post="/resumes/api/upload" 
                          hx-target="#uploadResult" 
                          hx-swap="innerHTML">
                        
                        <div class="mb-4">
                            <label for="title" class="form-label">Resume Title (Optional)</label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   placeholder="e.g., Software Engineer Resume">
                            <div class="form-text">If not provided, we'll generate a title based on your resume content.</div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Upload File</label>
                            <div class="file-upload-area" id="fileUploadArea">
                                <input type="file" class="form-control" id="file" name="file" 
                                       accept=".pdf,.docx,.doc" required style="display: none;">
                                <div class="text-center" id="uploadPrompt">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h5>Drag & drop your resume here</h5>
                                    <p class="text-muted mb-3">or <a href="#" id="browseFiles">browse files</a></p>
                                    <small class="text-muted">Supported formats: PDF, DOCX, DOC (Max 10MB)</small>
                                </div>
                                <div id="fileInfo" style="display: none;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file fa-2x text-primary me-3"></i>
                                        <div>
                                            <div id="fileName" class="fw-bold"></div>
                                            <div id="fileSize" class="text-muted small"></div>
                                        </div>
                                        <button type="button" class="btn btn-outline-danger btn-sm ms-auto" id="removeFile">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="uploadResult" class="mb-3"></div>

                        <div class="d-flex justify-content-between">
                            <a href="/resumes" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Resumes
                            </a>
                            <button type="submit" class="btn btn-primary" id="uploadBtn" disabled>
                                <span class="htmx-indicator spinner-border spinner-border-sm me-2" role="status"></span>
                                <i class="fas fa-upload me-2"></i>Upload & Parse
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tips Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Tips for Best Results</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Use a well-formatted resume with clear section headers
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Ensure text is selectable (not scanned images)
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Include standard sections: Experience, Education, Skills
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Use consistent formatting throughout the document
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('file');
    const fileUploadArea = document.getElementById('fileUploadArea');
    const uploadPrompt = document.getElementById('uploadPrompt');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const browseFiles = document.getElementById('browseFiles');
    const removeFile = document.getElementById('removeFile');
    const uploadBtn = document.getElementById('uploadBtn');

    // File input change handler
    fileInput.addEventListener('change', handleFileSelect);
    
    // Browse files click handler
    browseFiles.addEventListener('click', function(e) {
        e.preventDefault();
        fileInput.click();
    });

    // Remove file handler
    removeFile.addEventListener('click', function() {
        fileInput.value = '';
        showUploadPrompt();
    });

    // Drag and drop handlers
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect();
        }
    });

    function handleFileSelect() {
        const file = fileInput.files[0];
        if (file) {
            // Validate file type
            const allowedTypes = ['.pdf', '.docx', '.doc'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            
            if (!allowedTypes.includes(fileExtension)) {
                alert('Please select a PDF, DOCX, or DOC file.');
                fileInput.value = '';
                return;
            }

            // Validate file size (10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('File size must be less than 10MB.');
                fileInput.value = '';
                return;
            }

            showFileInfo(file);
        } else {
            showUploadPrompt();
        }
    }

    function showFileInfo(file) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        uploadPrompt.style.display = 'none';
        fileInfo.style.display = 'block';
        uploadBtn.disabled = false;
    }

    function showUploadPrompt() {
        uploadPrompt.style.display = 'block';
        fileInfo.style.display = 'none';
        uploadBtn.disabled = true;
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Handle upload response
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.target.id === 'uploadResult') {
            const xhr = event.detail.xhr;
            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                document.getElementById('uploadResult').innerHTML = `
                    <div class="alert alert-success">
                        <h6>Upload Successful!</h6>
                        <p class="mb-2">${response.message}</p>
                        <p class="mb-2"><strong>Parsed sections:</strong> ${response.parsed_sections.join(', ')}</p>
                        <a href="/resumes/${response.resume_id}" class="btn btn-primary btn-sm">View Resume</a>
                    </div>
                `;
            } else {
                const errorData = JSON.parse(xhr.responseText);
                document.getElementById('uploadResult').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Upload Failed:</strong> ${errorData.detail || 'Unknown error occurred'}
                    </div>
                `;
            }
        }
    });
});
</script>
{% endblock %}
