"""Main FastAPI application."""

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import os

from app.config import settings
from app.database import engine, Base

# Create database tables
Base.metadata.create_all(bind=engine)

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    description="AI-powered resume optimization with ATS-friendly templates",
    version=settings.version,
    debug=settings.debug,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure appropriately for production
)

# Create upload directory if it doesn't exist
os.makedirs(settings.upload_dir, exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Setup Jinja2 templates
templates = Jinja2Templates(directory="app/templates")

# Include routers
from app.routers import auth
app.include_router(auth.router, prefix="/auth", tags=["authentication"])
# Additional routers will be added as we create them
# app.include_router(resumes.router, prefix="/resumes", tags=["resumes"])
# app.include_router(template_router.router, prefix="/templates", tags=["templates"])
# app.include_router(analyzer.router, prefix="/analyzer", tags=["analyzer"])


@app.get("/")
async def home(request: Request):
    """Home page."""
    try:
        return templates.TemplateResponse(
            "index.html",
            {"request": request, "title": "Welcome to Resume Optimizer"}
        )
    except Exception as e:
        return {"message": "Welcome to Resume Optimizer", "status": "working", "error": str(e)}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": settings.version}


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """Handle 404 errors."""
    return templates.TemplateResponse(
        "errors/404.html",
        {"request": request},
        status_code=404
    )


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    """Handle 500 errors."""
    return templates.TemplateResponse(
        "errors/500.html",
        {"request": request},
        status_code=500
    )
