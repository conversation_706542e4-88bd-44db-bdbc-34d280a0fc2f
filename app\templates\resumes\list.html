{% extends "base.html" %}

{% block title %}My Resumes - Resume Optimizer{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>My Resumes</h2>
        <div>
            <a href="/resumes/upload" class="btn btn-outline-primary me-2">
                <i class="fas fa-upload me-2"></i>Upload Resume
            </a>
            <a href="/resumes/create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create New
            </a>
        </div>
    </div>

    {% if resumes %}
    <div class="row">
        {% for resume in resumes %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ resume.title }}</h5>
                    
                    <div class="mb-3">
                        {% if resume.original_filename %}
                        <small class="text-muted">
                            <i class="fas fa-file me-1"></i>{{ resume.original_filename }}
                        </small>
                        {% endif %}
                        
                        {% if resume.file_type %}
                        <span class="badge bg-secondary ms-2">{{ resume.file_type.upper() }}</span>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Updated {{ resume.updated_at.strftime('%b %d, %Y') if resume.updated_at else resume.created_at.strftime('%b %d, %Y') }}
                        </small>
                    </div>

                    {% if resume.optimization_score %}
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <small class="me-2">Optimization Score:</small>
                            <div class="progress flex-grow-1" style="height: 8px;">
                                <div class="progress-bar 
                                    {% if resume.optimization_score >= 80 %}bg-success
                                    {% elif resume.optimization_score >= 60 %}bg-warning
                                    {% else %}bg-danger{% endif %}" 
                                    style="width: {{ resume.optimization_score }}%"></div>
                            </div>
                            <small class="ms-2">{{ resume.optimization_score }}%</small>
                        </div>
                    </div>
                    {% endif %}

                    <div class="d-flex justify-content-between">
                        <a href="/resumes/{{ resume.id }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>View
                        </a>
                        <a href="/resumes/{{ resume.id }}/edit" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                        <button class="btn btn-outline-danger btn-sm" 
                                hx-delete="/resumes/api/{{ resume.id }}"
                                hx-confirm="Are you sure you want to delete this resume?"
                                hx-target="closest .col-md-6"
                                hx-swap="outerHTML">
                            <i class="fas fa-trash me-1"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="fas fa-file-alt fa-4x text-muted"></i>
        </div>
        <h4 class="text-muted mb-3">No resumes yet</h4>
        <p class="text-muted mb-4">Get started by uploading an existing resume or creating a new one from scratch.</p>
        <div>
            <a href="/resumes/upload" class="btn btn-outline-primary me-2">
                <i class="fas fa-upload me-2"></i>Upload Resume
            </a>
            <a href="/resumes/create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create New Resume
            </a>
        </div>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle successful deletion
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 200 && event.detail.requestConfig.verb === 'delete') {
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                Resume deleted successfully!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
        }
    });
});
</script>
{% endblock %}
