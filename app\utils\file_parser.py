"""File parsing utilities for extracting text from PDF and DOCX files."""

import os
import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path
try:
    import PyPDF2
except ImportError:
    PyPDF2 = None
from docx import Document
import logging

logger = logging.getLogger(__name__)


class ResumeParser:
    """Parser for extracting structured data from resume files."""
    
    def __init__(self):
        self.supported_formats = {'.pdf', '.docx', '.doc'}
    
    def is_supported_format(self, filename: str) -> bool:
        """Check if file format is supported."""
        return Path(filename).suffix.lower() in self.supported_formats
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file."""
        if PyPDF2 is None:
            raise ValueError("PyPDF2 is not installed. Please install it to parse PDF files.")

        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {str(e)}")
            raise ValueError(f"Failed to extract text from PDF: {str(e)}")
    
    def extract_text_from_docx(self, file_path: str) -> str:
        """Extract text from DOCX file."""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from DOCX {file_path}: {str(e)}")
            raise ValueError(f"Failed to extract text from DOCX: {str(e)}")
    
    def extract_text(self, file_path: str) -> str:
        """Extract text from supported file formats."""
        file_extension = Path(file_path).suffix.lower()
        
        if file_extension == '.pdf':
            return self.extract_text_from_pdf(file_path)
        elif file_extension in ['.docx', '.doc']:
            return self.extract_text_from_docx(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")
    
    def extract_email(self, text: str) -> Optional[str]:
        """Extract email address from text."""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        matches = re.findall(email_pattern, text)
        return matches[0] if matches else None
    
    def extract_phone(self, text: str) -> Optional[str]:
        """Extract phone number from text."""
        # Various phone number patterns
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # ************ or ************ or 1234567890
            r'\(\d{3}\)\s*\d{3}[-.]?\d{4}',   # (*************
            r'\+\d{1,3}[-.\s]?\d{3}[-.\s]?\d{3}[-.\s]?\d{4}',  # ******-456-7890
        ]
        
        for pattern in phone_patterns:
            matches = re.findall(pattern, text)
            if matches:
                return matches[0]
        return None
    
    def extract_urls(self, text: str) -> List[str]:
        """Extract URLs from text."""
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        return re.findall(url_pattern, text)
    
    def extract_sections(self, text: str) -> Dict[str, str]:
        """Extract common resume sections."""
        sections = {}
        
        # Common section headers
        section_patterns = {
            'summary': r'(?i)(summary|profile|objective|about|overview)[\s\n]*:?[\s\n]*(.*?)(?=\n\s*[A-Z][A-Z\s]+:|$)',
            'experience': r'(?i)(experience|employment|work history|professional experience)[\s\n]*:?[\s\n]*(.*?)(?=\n\s*[A-Z][A-Z\s]+:|$)',
            'education': r'(?i)(education|academic|qualifications)[\s\n]*:?[\s\n]*(.*?)(?=\n\s*[A-Z][A-Z\s]+:|$)',
            'skills': r'(?i)(skills|technical skills|competencies|expertise)[\s\n]*:?[\s\n]*(.*?)(?=\n\s*[A-Z][A-Z\s]+:|$)',
            'certifications': r'(?i)(certifications?|certificates?|licenses?)[\s\n]*:?[\s\n]*(.*?)(?=\n\s*[A-Z][A-Z\s]+:|$)',
            'projects': r'(?i)(projects?|portfolio)[\s\n]*:?[\s\n]*(.*?)(?=\n\s*[A-Z][A-Z\s]+:|$)',
        }
        
        for section_name, pattern in section_patterns.items():
            match = re.search(pattern, text, re.DOTALL)
            if match:
                sections[section_name] = match.group(2).strip()
        
        return sections
    
    def parse_resume(self, file_path: str) -> Dict:
        """Parse resume file and extract structured data."""
        try:
            # Extract raw text
            raw_text = self.extract_text(file_path)
            
            # Extract basic information
            email = self.extract_email(raw_text)
            phone = self.extract_phone(raw_text)
            urls = self.extract_urls(raw_text)
            
            # Extract sections
            sections = self.extract_sections(raw_text)
            
            # Extract name (first few lines, excluding email/phone)
            lines = raw_text.split('\n')
            name = None
            for line in lines[:5]:  # Check first 5 lines
                line = line.strip()
                if line and not any(x in line.lower() for x in ['email', 'phone', '@', 'http']):
                    if len(line.split()) <= 4:  # Likely a name
                        name = line
                        break
            
            return {
                'raw_text': raw_text,
                'personal_info': {
                    'name': name,
                    'email': email,
                    'phone': phone,
                    'urls': urls
                },
                'sections': sections,
                'file_info': {
                    'filename': Path(file_path).name,
                    'size': os.path.getsize(file_path),
                    'format': Path(file_path).suffix.lower()
                }
            }
            
        except Exception as e:
            logger.error(f"Error parsing resume {file_path}: {str(e)}")
            raise ValueError(f"Failed to parse resume: {str(e)}")


def validate_file_size(file_size: int, max_size: int = 10 * 1024 * 1024) -> bool:
    """Validate file size (default 10MB)."""
    return file_size <= max_size


def validate_file_type(filename: str, allowed_extensions: set = {'.pdf', '.docx', '.doc'}) -> bool:
    """Validate file type."""
    return Path(filename).suffix.lower() in allowed_extensions


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage."""
    # Remove or replace unsafe characters
    filename = re.sub(r'[^\w\s.-]', '', filename)
    # Replace spaces with underscores
    filename = re.sub(r'\s+', '_', filename)
    # Limit length
    name, ext = os.path.splitext(filename)
    if len(name) > 100:
        name = name[:100]
    return name + ext
