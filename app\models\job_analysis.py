"""Job analysis and matching models."""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class JobAnalysis(Base):
    """Job analysis model for storing job description analysis results."""
    
    __tablename__ = "job_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Job information
    job_title = Column(String(255), nullable=False)
    company_name = Column(String(255), nullable=True)
    job_description = Column(Text, nullable=False)
    job_url = Column(String(1000), nullable=True)
    
    # Analysis results
    extracted_keywords = Column(JSON, nullable=True)  # Array of keywords with weights
    required_skills = Column(JSON, nullable=True)  # Array of required skills
    preferred_skills = Column(JSON, nullable=True)  # Array of preferred skills
    experience_level = Column(String(50), nullable=True)  # entry, mid, senior, etc.
    education_requirements = Column(JSON, nullable=True)
    certifications_mentioned = Column(JSON, nullable=True)
    
    # AI analysis metadata
    analysis_version = Column(String(20), default="1.0")
    confidence_score = Column(Float, nullable=True)
    processing_time = Column(Float, nullable=True)  # seconds
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="job_analyses")
    job_matches = relationship("JobMatch", back_populates="job_analysis", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<JobAnalysis(id={self.id}, job_title='{self.job_title}')>"


class JobMatch(Base):
    """Job match model for storing resume-job matching results."""
    
    __tablename__ = "job_matches"
    
    id = Column(Integer, primary_key=True, index=True)
    resume_id = Column(Integer, ForeignKey("resumes.id"), nullable=False)
    job_analysis_id = Column(Integer, ForeignKey("job_analyses.id"), nullable=False)
    
    # Matching scores
    overall_match_score = Column(Float, nullable=False)
    keyword_match_score = Column(Float, nullable=True)
    skills_match_score = Column(Float, nullable=True)
    experience_match_score = Column(Float, nullable=True)
    education_match_score = Column(Float, nullable=True)
    
    # Detailed matching results
    matched_keywords = Column(JSON, nullable=True)  # Keywords found in resume
    missing_keywords = Column(JSON, nullable=True)  # Keywords missing from resume
    matched_skills = Column(JSON, nullable=True)
    missing_skills = Column(JSON, nullable=True)
    
    # Recommendations
    improvement_suggestions = Column(JSON, nullable=True)
    keyword_suggestions = Column(JSON, nullable=True)
    section_recommendations = Column(JSON, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    resume = relationship("Resume", back_populates="job_matches")
    job_analysis = relationship("JobAnalysis", back_populates="job_matches")
    
    def __repr__(self):
        return f"<JobMatch(id={self.id}, score={self.overall_match_score})>"
