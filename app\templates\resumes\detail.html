{% extends "base.html" %}

{% block title %}{{ resume.title }} - Resume Optimizer{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8">
            <!-- Resume Content -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ resume.title }}</h4>
                    <div>
                        <a href="/resumes/{{ resume.id }}/edit" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/resumes/{{ resume.id }}/export/pdf">
                                    <i class="fas fa-file-pdf me-2"></i>PDF
                                </a></li>
                                <li><a class="dropdown-item" href="/resumes/{{ resume.id }}/export/docx">
                                    <i class="fas fa-file-word me-2"></i>DOCX
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Personal Information -->
                    {% if resume.personal_info %}
                    <div class="text-center mb-4 pb-3 border-bottom">
                        {% if resume.personal_info.name %}
                        <h2 class="mb-2">{{ resume.personal_info.name }}</h2>
                        {% endif %}
                        
                        <div class="row justify-content-center">
                            {% if resume.personal_info.email %}
                            <div class="col-auto">
                                <i class="fas fa-envelope text-muted me-1"></i>
                                <a href="mailto:{{ resume.personal_info.email }}">{{ resume.personal_info.email }}</a>
                            </div>
                            {% endif %}
                            
                            {% if resume.personal_info.phone %}
                            <div class="col-auto">
                                <i class="fas fa-phone text-muted me-1"></i>
                                {{ resume.personal_info.phone }}
                            </div>
                            {% endif %}
                            
                            {% if resume.personal_info.location %}
                            <div class="col-auto">
                                <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                {{ resume.personal_info.location }}
                            </div>
                            {% endif %}
                        </div>
                        
                        {% if resume.personal_info.linkedin_url or resume.personal_info.website_url %}
                        <div class="row justify-content-center mt-2">
                            {% if resume.personal_info.linkedin_url %}
                            <div class="col-auto">
                                <i class="fab fa-linkedin text-muted me-1"></i>
                                <a href="{{ resume.personal_info.linkedin_url }}" target="_blank">LinkedIn</a>
                            </div>
                            {% endif %}
                            
                            {% if resume.personal_info.website_url %}
                            <div class="col-auto">
                                <i class="fas fa-globe text-muted me-1"></i>
                                <a href="{{ resume.personal_info.website_url }}" target="_blank">Website</a>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Professional Summary -->
                    {% if resume.summary %}
                    <div class="mb-4">
                        <h5 class="text-primary border-bottom pb-2 mb-3">Professional Summary</h5>
                        <p>{{ resume.summary }}</p>
                    </div>
                    {% endif %}

                    <!-- Work Experience -->
                    {% if resume.work_experience %}
                    <div class="mb-4">
                        <h5 class="text-primary border-bottom pb-2 mb-3">Work Experience</h5>
                        {% for exp in resume.work_experience %}
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ exp.position or exp.get('position', '') }}</h6>
                                    <div class="text-muted">{{ exp.company or exp.get('company', '') }}</div>
                                </div>
                                <div class="text-end text-muted small">
                                    {% if exp.start_date or exp.get('start_date') %}
                                    {{ exp.start_date or exp.get('start_date', '') }}
                                    {% if exp.end_date or exp.get('end_date') %}
                                    - {{ exp.end_date or exp.get('end_date', '') }}
                                    {% endif %}
                                    {% endif %}
                                    {% if exp.location or exp.get('location') %}
                                    <br>{{ exp.location or exp.get('location', '') }}
                                    {% endif %}
                                </div>
                            </div>
                            {% if exp.description or exp.get('description') %}
                            <div class="mt-2">
                                {{ exp.description or exp.get('description', '') }}
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Education -->
                    {% if resume.education %}
                    <div class="mb-4">
                        <h5 class="text-primary border-bottom pb-2 mb-3">Education</h5>
                        {% for edu in resume.education %}
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ edu.degree or edu.get('degree', '') }}</h6>
                                    <div class="text-muted">{{ edu.institution or edu.get('institution', '') }}</div>
                                    {% if edu.field or edu.get('field') %}
                                    <div class="text-muted small">{{ edu.field or edu.get('field', '') }}</div>
                                    {% endif %}
                                </div>
                                <div class="text-end text-muted small">
                                    {% if edu.graduation_date or edu.get('graduation_date') %}
                                    {{ edu.graduation_date or edu.get('graduation_date', '') }}
                                    {% endif %}
                                    {% if edu.gpa or edu.get('gpa') %}
                                    <br>GPA: {{ edu.gpa or edu.get('gpa', '') }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Skills -->
                    {% if resume.skills %}
                    <div class="mb-4">
                        <h5 class="text-primary border-bottom pb-2 mb-3">Skills</h5>
                        <div class="d-flex flex-wrap gap-2">
                            {% for skill in resume.skills %}
                            <span class="badge bg-light text-dark border">{{ skill }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Certifications -->
                    {% if resume.certifications %}
                    <div class="mb-4">
                        <h5 class="text-primary border-bottom pb-2 mb-3">Certifications</h5>
                        {% for cert in resume.certifications %}
                        <div class="mb-2">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ cert.name or cert.get('name', '') }}</h6>
                                    <div class="text-muted">{{ cert.issuer or cert.get('issuer', '') }}</div>
                                </div>
                                <div class="text-end text-muted small">
                                    {% if cert.date or cert.get('date') %}
                                    {{ cert.date or cert.get('date', '') }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Projects -->
                    {% if resume.projects %}
                    <div class="mb-4">
                        <h5 class="text-primary border-bottom pb-2 mb-3">Projects</h5>
                        {% for project in resume.projects %}
                        <div class="mb-3">
                            <h6 class="mb-1">{{ project.name or project.get('name', '') }}</h6>
                            {% if project.description or project.get('description') %}
                            <p class="mb-2">{{ project.description or project.get('description', '') }}</p>
                            {% endif %}
                            {% if project.technologies or project.get('technologies') %}
                            <div class="mb-2">
                                <strong>Technologies:</strong> 
                                {% if project.technologies is string %}
                                {{ project.technologies }}
                                {% else %}
                                {{ project.technologies|join(', ') if project.technologies else project.get('technologies', [])|join(', ') }}
                                {% endif %}
                            </div>
                            {% endif %}
                            {% if project.url or project.get('url') %}
                            <div>
                                <a href="{{ project.url or project.get('url', '') }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-external-link-alt me-1"></i>View Project
                                </a>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Resume Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Resume Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        {% if resume.optimization_score %}
                        <div class="col-6 mb-3">
                            <div class="score-circle 
                                {% if resume.optimization_score >= 80 %}score-excellent
                                {% elif resume.optimization_score >= 60 %}score-good
                                {% elif resume.optimization_score >= 40 %}score-fair
                                {% else %}score-poor{% endif %} mx-auto">
                                {{ resume.optimization_score }}%
                            </div>
                            <small class="text-muted">Optimization Score</small>
                        </div>
                        {% endif %}
                        
                        {% if resume.ats_score %}
                        <div class="col-6 mb-3">
                            <div class="score-circle 
                                {% if resume.ats_score >= 80 %}score-excellent
                                {% elif resume.ats_score >= 60 %}score-good
                                {% elif resume.ats_score >= 40 %}score-fair
                                {% else %}score-poor{% endif %} mx-auto">
                                {{ resume.ats_score }}%
                            </div>
                            <small class="text-muted">ATS Score</small>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mt-3">
                        <div class="d-flex justify-content-between mb-2">
                            <small>Version:</small>
                            <small>{{ resume.version }}</small>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <small>Created:</small>
                            <small>{{ resume.created_at.strftime('%b %d, %Y') }}</small>
                        </div>
                        {% if resume.updated_at %}
                        <div class="d-flex justify-content-between mb-2">
                            <small>Updated:</small>
                            <small>{{ resume.updated_at.strftime('%b %d, %Y') }}</small>
                        </div>
                        {% endif %}
                        {% if resume.last_optimized_at %}
                        <div class="d-flex justify-content-between">
                            <small>Last Optimized:</small>
                            <small>{{ resume.last_optimized_at.strftime('%b %d, %Y') }}</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/analyzer?resume_id={{ resume.id }}" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>Analyze with Job Description
                        </a>
                        <button class="btn btn-outline-success" 
                                hx-post="/resumes/api/{{ resume.id }}/optimize"
                                hx-target="#optimizeResult"
                                hx-swap="innerHTML">
                            <i class="fas fa-magic me-2"></i>AI Optimize
                        </button>
                        <a href="/resumes/{{ resume.id }}/duplicate" class="btn btn-outline-info">
                            <i class="fas fa-copy me-2"></i>Duplicate Resume
                        </a>
                    </div>
                    <div id="optimizeResult" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
