"""Optimization history and AI interaction models."""

from sqlalchemy import <PERSON>um<PERSON>, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class OptimizationHistory(Base):
    """Optimization history model for tracking AI optimization sessions."""
    
    __tablename__ = "optimization_history"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    resume_id = Column(Integer, ForeignKey("resumes.id"), nullable=False)
    
    # Optimization details
    optimization_type = Column(String(50), nullable=False)  # ats, keywords, general, job_specific
    target_job_title = Column(String(255), nullable=True)
    target_industry = Column(String(100), nullable=True)
    
    # Before and after data
    original_content = Column(JSON, nullable=True)  # Resume content before optimization
    optimized_content = Column(JSON, nullable=True)  # Resume content after optimization
    changes_made = Column(JSON, nullable=True)  # Detailed list of changes
    
    # Scores and metrics
    original_score = Column(Float, nullable=True)
    optimized_score = Column(Float, nullable=True)
    improvement_percentage = Column(Float, nullable=True)
    
    # AI interaction data
    ai_model_used = Column(String(50), nullable=True)  # gemini-pro, gpt-4, etc.
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True)
    processing_time = Column(Float, nullable=True)  # seconds
    
    # User feedback
    user_rating = Column(Integer, nullable=True)  # 1-5 stars
    user_feedback = Column(Text, nullable=True)
    applied_suggestions = Column(JSON, nullable=True)  # Which suggestions were applied
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="optimization_history")
    resume = relationship("Resume", back_populates="optimization_history")
    
    def __repr__(self):
        return f"<OptimizationHistory(id={self.id}, type='{self.optimization_type}')>"


class AIInteraction(Base):
    """AI interaction model for tracking all AI API calls and responses."""
    
    __tablename__ = "ai_interactions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Interaction details
    interaction_type = Column(String(50), nullable=False)  # resume_analysis, job_analysis, optimization
    ai_provider = Column(String(50), nullable=False)  # gemini, openai, etc.
    model_name = Column(String(100), nullable=False)
    
    # Request data
    prompt = Column(Text, nullable=False)
    input_data = Column(JSON, nullable=True)
    parameters = Column(JSON, nullable=True)  # temperature, max_tokens, etc.
    
    # Response data
    response = Column(Text, nullable=True)
    response_data = Column(JSON, nullable=True)
    
    # Metrics
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True)
    total_tokens = Column(Integer, nullable=True)
    processing_time = Column(Float, nullable=True)  # seconds
    cost = Column(Float, nullable=True)  # estimated cost in USD
    
    # Status and error handling
    status = Column(String(20), default="pending")  # pending, completed, failed
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<AIInteraction(id={self.id}, type='{self.interaction_type}', status='{self.status}')>"
