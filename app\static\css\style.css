/* Custom styles for Resume Optimizer */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Forms */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* File upload area */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.15s ease-in-out;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

/* Resume templates */
.template-card {
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
}

.template-card:hover {
    transform: translateY(-2px);
}

.template-preview {
    height: 300px;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

/* Score indicators */
.score-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
}

.score-excellent {
    background-color: var(--success-color);
}

.score-good {
    background-color: var(--info-color);
}

.score-fair {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.score-poor {
    background-color: var(--danger-color);
}

/* Progress bars */
.progress {
    height: 0.75rem;
    border-radius: 0.375rem;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* HTMX loading indicators */
.htmx-indicator {
    opacity: 0;
    transition: opacity 500ms ease-in;
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

.htmx-request.htmx-indicator {
    opacity: 1;
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* Keyword highlighting */
.keyword-match {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 500;
}

.keyword-missing {
    background-color: #f8d7da;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 500;
}
