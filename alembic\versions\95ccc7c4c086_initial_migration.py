"""Initial migration

Revision ID: 95ccc7c4c086
Revises: 
Create Date: 2025-06-28 16:09:09.343348

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '95ccc7c4c086'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('resume_templates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('html_template', sa.Text(), nullable=False),
    sa.Column('css_styles', sa.Text(), nullable=True),
    sa.Column('preview_image', sa.String(length=500), nullable=True),
    sa.Column('is_ats_friendly', sa.<PERSON>(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_premium', sa.Boolean(), nullable=True),
    sa.Column('difficulty_level', sa.String(length=20), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('rating', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_resume_templates_id'), 'resume_templates', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('first_name', sa.String(length=100), nullable=True),
    sa.Column('last_name', sa.String(length=100), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('location', sa.String(length=255), nullable=True),
    sa.Column('linkedin_url', sa.String(length=500), nullable=True),
    sa.Column('website_url', sa.String(length=500), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_table('ai_interactions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('interaction_type', sa.String(length=50), nullable=False),
    sa.Column('ai_provider', sa.String(length=50), nullable=False),
    sa.Column('model_name', sa.String(length=100), nullable=False),
    sa.Column('prompt', sa.Text(), nullable=False),
    sa.Column('input_data', sa.JSON(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.Column('response', sa.Text(), nullable=True),
    sa.Column('response_data', sa.JSON(), nullable=True),
    sa.Column('prompt_tokens', sa.Integer(), nullable=True),
    sa.Column('completion_tokens', sa.Integer(), nullable=True),
    sa.Column('total_tokens', sa.Integer(), nullable=True),
    sa.Column('processing_time', sa.Float(), nullable=True),
    sa.Column('cost', sa.Float(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_interactions_id'), 'ai_interactions', ['id'], unique=False)
    op.create_table('job_analyses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('job_title', sa.String(length=255), nullable=False),
    sa.Column('company_name', sa.String(length=255), nullable=True),
    sa.Column('job_description', sa.Text(), nullable=False),
    sa.Column('job_url', sa.String(length=1000), nullable=True),
    sa.Column('extracted_keywords', sa.JSON(), nullable=True),
    sa.Column('required_skills', sa.JSON(), nullable=True),
    sa.Column('preferred_skills', sa.JSON(), nullable=True),
    sa.Column('experience_level', sa.String(length=50), nullable=True),
    sa.Column('education_requirements', sa.JSON(), nullable=True),
    sa.Column('certifications_mentioned', sa.JSON(), nullable=True),
    sa.Column('analysis_version', sa.String(length=20), nullable=True),
    sa.Column('confidence_score', sa.Float(), nullable=True),
    sa.Column('processing_time', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_job_analyses_id'), 'job_analyses', ['id'], unique=False)
    op.create_table('resumes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('personal_info', sa.JSON(), nullable=True),
    sa.Column('summary', sa.Text(), nullable=True),
    sa.Column('work_experience', sa.JSON(), nullable=True),
    sa.Column('education', sa.JSON(), nullable=True),
    sa.Column('skills', sa.JSON(), nullable=True),
    sa.Column('certifications', sa.JSON(), nullable=True),
    sa.Column('projects', sa.JSON(), nullable=True),
    sa.Column('languages', sa.JSON(), nullable=True),
    sa.Column('additional_sections', sa.JSON(), nullable=True),
    sa.Column('original_filename', sa.String(length=255), nullable=True),
    sa.Column('file_path', sa.String(length=500), nullable=True),
    sa.Column('file_type', sa.String(length=10), nullable=True),
    sa.Column('template_id', sa.Integer(), nullable=True),
    sa.Column('custom_styling', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_public', sa.Boolean(), nullable=True),
    sa.Column('version', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_optimized_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('optimization_score', sa.Float(), nullable=True),
    sa.Column('ats_score', sa.Float(), nullable=True),
    sa.Column('keyword_density', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['template_id'], ['resume_templates.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_resumes_id'), 'resumes', ['id'], unique=False)
    op.create_table('job_matches',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('resume_id', sa.Integer(), nullable=False),
    sa.Column('job_analysis_id', sa.Integer(), nullable=False),
    sa.Column('overall_match_score', sa.Float(), nullable=False),
    sa.Column('keyword_match_score', sa.Float(), nullable=True),
    sa.Column('skills_match_score', sa.Float(), nullable=True),
    sa.Column('experience_match_score', sa.Float(), nullable=True),
    sa.Column('education_match_score', sa.Float(), nullable=True),
    sa.Column('matched_keywords', sa.JSON(), nullable=True),
    sa.Column('missing_keywords', sa.JSON(), nullable=True),
    sa.Column('matched_skills', sa.JSON(), nullable=True),
    sa.Column('missing_skills', sa.JSON(), nullable=True),
    sa.Column('improvement_suggestions', sa.JSON(), nullable=True),
    sa.Column('keyword_suggestions', sa.JSON(), nullable=True),
    sa.Column('section_recommendations', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['job_analysis_id'], ['job_analyses.id'], ),
    sa.ForeignKeyConstraint(['resume_id'], ['resumes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_job_matches_id'), 'job_matches', ['id'], unique=False)
    op.create_table('optimization_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('resume_id', sa.Integer(), nullable=False),
    sa.Column('optimization_type', sa.String(length=50), nullable=False),
    sa.Column('target_job_title', sa.String(length=255), nullable=True),
    sa.Column('target_industry', sa.String(length=100), nullable=True),
    sa.Column('original_content', sa.JSON(), nullable=True),
    sa.Column('optimized_content', sa.JSON(), nullable=True),
    sa.Column('changes_made', sa.JSON(), nullable=True),
    sa.Column('original_score', sa.Float(), nullable=True),
    sa.Column('optimized_score', sa.Float(), nullable=True),
    sa.Column('improvement_percentage', sa.Float(), nullable=True),
    sa.Column('ai_model_used', sa.String(length=50), nullable=True),
    sa.Column('prompt_tokens', sa.Integer(), nullable=True),
    sa.Column('completion_tokens', sa.Integer(), nullable=True),
    sa.Column('processing_time', sa.Float(), nullable=True),
    sa.Column('user_rating', sa.Integer(), nullable=True),
    sa.Column('user_feedback', sa.Text(), nullable=True),
    sa.Column('applied_suggestions', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['resume_id'], ['resumes.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_optimization_history_id'), 'optimization_history', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_optimization_history_id'), table_name='optimization_history')
    op.drop_table('optimization_history')
    op.drop_index(op.f('ix_job_matches_id'), table_name='job_matches')
    op.drop_table('job_matches')
    op.drop_index(op.f('ix_resumes_id'), table_name='resumes')
    op.drop_table('resumes')
    op.drop_index(op.f('ix_job_analyses_id'), table_name='job_analyses')
    op.drop_table('job_analyses')
    op.drop_index(op.f('ix_ai_interactions_id'), table_name='ai_interactions')
    op.drop_table('ai_interactions')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_resume_templates_id'), table_name='resume_templates')
    op.drop_table('resume_templates')
    # ### end Alembic commands ###
